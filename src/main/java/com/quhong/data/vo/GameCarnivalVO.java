package com.quhong.data.vo;


import java.util.List;
import java.util.Set;

public class GameCarnivalVO extends OtherRankConfigVO {

    private TotalTaskVO totalTaskVO; // 累积任务
    private List<TaskConfigVO> dailyTaskList; // 每日任务
    private List<OtherRankingListVO> rankList; // 玩家榜单
    private OtherRankingListVO myRankVO; //
    private String recommendRoomId; // 推荐房间id

    public static class TotalInfo {
        private Set<String> daySet; // 完成签到的日期集合

        public Set<String> getDaySet() {
            return daySet;
        }

        public void setDaySet(Set<String> daySet) {
            this.daySet = daySet;
        }
    }

    public static class DetailInfo {
        private String dayStr;
        private int winDiamondNum1; // 在任意游戏中共赢得10钻
        private int winDiamondNum2; // 在任意游戏中共赢得100钻
        private int winDiamondNum3; // 在任意游戏中共赢得500钻
        private Set<String> winGameType; // 赢得的游戏类型
        private int winCarromCount; // 赢得克罗姆游戏局数

        public String getDayStr() {
            return dayStr;
        }

        public void setDayStr(String dayStr) {
            this.dayStr = dayStr;
        }

        public int getWinDiamondNum1() {
            return winDiamondNum1;
        }

        public void setWinDiamondNum1(int winDiamondNum1) {
            this.winDiamondNum1 = winDiamondNum1;
        }

        public int getWinDiamondNum2() {
            return winDiamondNum2;
        }

        public void setWinDiamondNum2(int winDiamondNum2) {
            this.winDiamondNum2 = winDiamondNum2;
        }

        public int getWinDiamondNum3() {
            return winDiamondNum3;
        }

        public void setWinDiamondNum3(int winDiamondNum3) {
            this.winDiamondNum3 = winDiamondNum3;
        }

        public Set<String> getWinGameType() {
            return winGameType;
        }

        public void setWinGameType(Set<String> winGameType) {
            this.winGameType = winGameType;
        }

        public int getWinCarromCount() {
            return winCarromCount;
        }

        public void setWinCarromCount(int winCarromCount) {
            this.winCarromCount = winCarromCount;
        }
    }

    public static class TotalTaskVO {
        private int totalDayCount;  // 累计打卡天数
        private int todayGameCount;  // 今日玩的游戏次数

        public int getTotalDayCount() {
            return totalDayCount;
        }

        public void setTotalDayCount(int totalDayCount) {
            this.totalDayCount = totalDayCount;
        }

        public int getTodayGameCount() {
            return todayGameCount;
        }

        public void setTodayGameCount(int todayGameCount) {
            this.todayGameCount = todayGameCount;
        }
    }


    public TotalTaskVO getTotalTaskVO() {
        return totalTaskVO;
    }

    public void setTotalTaskVO(TotalTaskVO totalTaskVO) {
        this.totalTaskVO = totalTaskVO;
    }

    public List<TaskConfigVO> getDailyTaskList() {
        return dailyTaskList;
    }

    public void setDailyTaskList(List<TaskConfigVO> dailyTaskList) {
        this.dailyTaskList = dailyTaskList;
    }

    public List<OtherRankingListVO> getRankList() {
        return rankList;
    }

    public void setRankList(List<OtherRankingListVO> rankList) {
        this.rankList = rankList;
    }

    public OtherRankingListVO getMyRankVO() {
        return myRankVO;
    }

    public void setMyRankVO(OtherRankingListVO myRankVO) {
        this.myRankVO = myRankVO;
    }

    public String getRecommendRoomId() {
        return recommendRoomId;
    }

    public void setRecommendRoomId(String recommendRoomId) {
        this.recommendRoomId = recommendRoomId;
    }
}

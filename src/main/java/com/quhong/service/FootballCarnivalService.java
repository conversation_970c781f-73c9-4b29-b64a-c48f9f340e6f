package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.*;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.dto.ShareActivityDTO;
import com.quhong.data.vo.FootballCarnivalVO;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 足球狂欢
 */

@Service
public class FootballCarnivalService extends OtherActivityService {


    private static final Logger logger = LoggerFactory.getLogger(FootballCarnivalService.class);
    // 个人数据信息key
    private static final String CHANCE_NUM_KEY = "chanceNum";                // 踢球机会数
    private static final String LEFT_BEAN_NUM_KEY = "leftBean";            // 剩余钻石数
    private static final String KICK_STAGE_NUM_KEY = "kickStageNum_%s";         // 当前阶段我的踢球次数
    // private static final String KICK_SUCCESS_STAGE_NUM_KEY = "kickSuccessStageNum_%s";         // 当前阶段我的踢球成功次数
    private static final String KICK_TOTAL_NUM_KEY = "kickTotalNum";         // 我的踢球总次数
    private static final String KICK_SUCCESS_TOTAL_NUM_KEY = "kickSuccessTotalNum"; // 我的踢球成功总次数
    private static final String TEAM_ID_KEY = "teamId";                    // 队伍id-Key
    private static final String SPECIAL_GIFT_RECEIVE_KEY = "specialGiftReceive"; // 收到特殊礼物


    // 团队数据key，用于判断团队相关奖励是否达成
    private static final String TEAM_KICK_TOTAL_NUM_KEY = "kickTotalNum";         // 队伍的踢球总次数
    private static final String TEAM_KICK_SUCCESS_TOTAL_NUM_KEY = "kickSuccessTotalNum"; // 队伍的踢球成功总次数

    // 当前阶段是否已执行结束逻辑
    private static final String STAGE_OVER_KEY = "stageOver_%s";         // 我的踢球总次数

    public static String ACTIVITY_ID = "689f373d97c739b14d747886";
    public static String ACTIVITY_URL = String.format("https://static.youstar.live/football_carnival/?activityId=%s", ACTIVITY_ID);
    private static final List<Integer> DRAW_NUM_LIST = Arrays.asList(1, 10, 50);
    private static final String POOL_SIZE_PAY_KEY = "2025FootballCarnivalDraw2";      // 付费用户key
    private static final String POOL_SIZE_NO_PAY_KEY = "2025FootballCarnivalDraw3";   // 非付费用户key
    private static final Integer LIMIT_INIT_POOL = 30;
    private static final Integer RECORD_PAGE_SIZE = 10;
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private static final Integer NO_GOAL_TYPE = -150; // 未进球,哭脸

    private static final Integer TEAM_MEMBER_REACH = 3; // 组队满3人
    private static final Integer TEAM_TOTAL_KICK_BALL_NUM_REACH = 50; // 队伍总计踢球次数
    private static final Integer TEAM_TOTAL_KICK_BALL_2_NUM_REACH = 100; // 队伍总计踢球次数2

    private static final List<Integer> STAGE_START_TIME_LIST = new ArrayList<>();

    private static final String SPECIAL_GIFT_RECEIVE_RES_KEY = "2025FootballCarnivalReceiver"; // 收到特殊礼物下发的资源key

    private static final String TEAM_TASK_MEMBER_REACH = "2025FootballCarnivalTeam";
    private static final String TEAM_TASK_TOTAL_KICK = "2025FootballCarnivalTeam1";
    private static final String TEAM_TASK_TOTAL_KICK_2 = "2025FootballCarnivalTeam2";

    private static final String TEAM_FINAL_WINNER_TOP1 = "2025FootballCarnivalTeamTop1";

    private static int SPECIAL_GIFT_ID = 1333;

    @Resource
    private EventReport eventReport;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Resource
    private CacheDataService cacheDataService;


    @PostConstruct
    public void init() {
        STAGE_START_TIME_LIST.clear();
        if (ServerConfig.isNotProduct()) {
            // 测试环境
            SPECIAL_GIFT_ID = 110;
            ACTIVITY_ID = "689ac0f06ddb42534ab05989";
            OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivityNull(ACTIVITY_ID);
            ACTIVITY_URL = String.format("https://test2.qmovies.tv/football_carnival/?activityId=%s", ACTIVITY_ID);
            if (activityData == null) {
                logger.error("init activityData is null ACTIVITY_ID:{}", ACTIVITY_ID);
                return;
            }
            int startTime = activityData.getStartTime();
            STAGE_START_TIME_LIST.add(startTime); // 晋级赛-活动开始时间
            int startTime2 = 1755244800;
            STAGE_START_TIME_LIST.add(startTime2); // 8->4 开始时间
            int startTime3 = 1755251400;
            STAGE_START_TIME_LIST.add(startTime3); // 4->2 开始时间
            STAGE_START_TIME_LIST.add(startTime3 + (int) TimeUnit.MINUTES.toSeconds(30)); // 2->1 开始时间
        } else {
            OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivityNull(ACTIVITY_ID);
            if (activityData == null) {
                logger.error("init activityData is null activityId:{}", ACTIVITY_ID);
                return;
            }
            int startTime = activityData.getStartTime();
            STAGE_START_TIME_LIST.add(startTime); // 晋级赛-活动开始时间,可能配置为5点开始

            if (activityData.getAcNameEn().startsWith("test")) {
                // 灰度测试
                STAGE_START_TIME_LIST.add(startTime + (int) TimeUnit.MINUTES.toSeconds(60)); // 8->4 开始时间
                STAGE_START_TIME_LIST.add(startTime + (int) TimeUnit.MINUTES.toSeconds(80)); // 4->2 开始时间
                STAGE_START_TIME_LIST.add(startTime + (int) TimeUnit.MINUTES.toSeconds(100)); // 2->1 开始时间
                // startTime + (int) TimeUnit.MINUTES.toSeconds(120); //活动结束时间
            } else {
                // 正式环境
                String startTimeStr = DateHelper.ARABIAN.formatDateInDay(new Date(startTime * 1000L));
                int startTime2 = DateHelper.ARABIAN.stringDateToStampSecond(startTimeStr); // 凌晨0开始
                STAGE_START_TIME_LIST.add(startTime2 + (int) TimeUnit.DAYS.toSeconds(5));//  8->4  开始时间
                STAGE_START_TIME_LIST.add(startTime2 + (int) TimeUnit.DAYS.toSeconds(6)); // 4->2 开始时间
                STAGE_START_TIME_LIST.add(startTime2 + (int) TimeUnit.DAYS.toSeconds(7)); // 2->1开始时间
            }
        }
        logger.info("STAGE_START_TIME_LIST:{}", STAGE_START_TIME_LIST);
    }


    /**
     * @param currentTime
     * @return -1:不在活动时间内 取值为1-4
     * 1: 晋级赛  2: 8->4  3: 4->2  4: 2->1
     */
    private int getStageIndex(String activityId, int currentTime) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(activityId);
        if (activityData == null || currentTime < activityData.getStartTime()) {
            return -1;
        }
        int stageIndex = getNewBaseIndexLevel(currentTime, STAGE_START_TIME_LIST);
        stageIndex += 1;
        return stageIndex;
    }

    // 抽奖相关的每日key
    // private String getDailyDate(String activityId) {
    //     return String.format("dailyDate:%s", activityId);
    // }

    // 个人数据信息key
    private String getHashActivityId(String activityId, String uid) {
        return String.format("footballCarnival:%s:%s", activityId, uid);
    }

    // 团队成员key
    private String getListTeamMemberKey(String activityId, String teamId) {
        return String.format("teamMember:%s:%s", activityId, teamId);
    }

    // // 团队榜单key
    // private String getZSetTeamRankKey(String activityId) {
    //     return String.format("teamRank:%s", activityId);
    // }

    // 团队榜单key,分阶段
    private String getZSetTeamRankKey(String activityId, int stageIndex) {
        return String.format("teamRank:%s:stageIndex:%s", activityId, stageIndex);
    }


    // 团队数据信息key ，flied用于统计整个活动团队成就奖励
    private String getHashActivityIdTeam(String activityId, String teamId) {
        return String.format("footballCarnivalTeam:%s:%s", activityId, teamId);
    }

    // 团队pk数据信息key 存每一轮的对阵
    private String getHashPkActivityId(String activityId) {
        return String.format("footballCarnivalTeamPk:%s", activityId);
    }

    //  存进入pk的队伍id
    private String getSetTeamPkKey(String activityId, int stageIndex) {
        return String.format("footballCarnivalTeamPk:%s:stageIndex:%s", activityId, stageIndex);
    }

    // 抽奖品Key
    // private String getListDrawPrizeKey(String activityId, String resKey) {
    //     return String.format("drawPrize:%s:%s", activityId, resKey);
    // }

    // 滚动记录
    private String getListScrollKey(String activityId) {
        return String.format("scroll:%s", activityId);
    }

    // 历史记录Key
    private String getListHistoryDrawKey(String activityId, String uid) {
        return String.format("historyDraw:%s:%s", activityId, uid);
    }

    // 限制每日分享次数
    private String getShareLimitKey(String activityId, String uid, String aid, String dayStr) {
        return String.format("shareLimit:%s:%s:%s:%s", activityId, uid, aid, dayStr);
    }


//    private String getLocalKey(String uid) {
//        return String.format("footballCarnival:%s", uid);
//    }

    private String getTeamLocalKey(String teamId) {
        return String.format("incTeamTrainScore:%s", teamId);
    }

    private String getTeamJoinLocalKey(String teamId) {
        return String.format("footballCarnivalJoinTeam:%s", teamId);
    }


    public FootballCarnivalVO footballCarnivalConfig(String activityId, String uid) {
        FootballCarnivalVO vo = new FootballCarnivalVO();
        OtherRankingActivityData activityData = getOtherRankingActivityNull(activityId);
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());
        String hashActivityId = getHashActivityId(activityId, uid);
        Map<String, String> userDataMap = activityCommonRedis.getCommonHashAllMapStr(hashActivityId);
        vo.setKickBallChanceNum(Integer.valueOf(userDataMap.getOrDefault(CHANCE_NUM_KEY, "0")));
        int stageIndex = getStageIndex(activityId, DateHelper.getNowSeconds());
        if (stageIndex != -1) {
            vo.setStageStartTime(STAGE_START_TIME_LIST.get(stageIndex - 1));
            if (stageIndex == STAGE_START_TIME_LIST.size()) {
                vo.setStageEndTime(activityData.getEndTime());
            } else {
                vo.setStageEndTime(STAGE_START_TIME_LIST.get(stageIndex));
            }
        }
        vo.setStage(stageIndex);
        vo.setKickBallTotalNum(Integer.valueOf(userDataMap.getOrDefault(String.format(KICK_STAGE_NUM_KEY, stageIndex), "0")));

        String teamId = userDataMap.get(TEAM_ID_KEY);
        FootballCarnivalVO.FootballCarnivalTeamConfig teamInfoVO = new FootballCarnivalVO.FootballCarnivalTeamConfig();
        if (ObjectUtils.isEmpty(teamId)) {
            // 未组队成功则显示只有自己在队伍中
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            teamInfoVO.setTeamName(actorData.getName());
            teamInfoVO.setTeamLeadUid(uid);
            List<FootballCarnivalVO.TeamInfo> teamMemberList = new ArrayList<>();
            FootballCarnivalVO.TeamInfo teamInfo = new FootballCarnivalVO.TeamInfo();
            teamInfo.setUid(uid);
            teamInfo.setName(actorData.getName());
            teamInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            teamInfo.setScore(0);
            teamMemberList.add(teamInfo);
            teamInfoVO.setTeamMemberList(teamMemberList);
            teamInfoVO.setTeamScore(0);
        } else {
            List<String> teamMemberUidList = activityCommonRedis.getCommonListRecord(getListTeamMemberKey(activityId, teamId), 3);
            List<FootballCarnivalVO.TeamInfo> teamMemberList = new ArrayList<>();
            teamInfoVO.setTeamId(teamId);
            for (int i = 0; i < teamMemberUidList.size(); i++) {
                String memberUid = teamMemberUidList.get(i);
                ActorData actorData = actorDao.getActorDataFromCache(memberUid);
                if (i == 0) {
                    teamInfoVO.setTeamName(actorData.getName());
                    teamInfoVO.setTeamLeadUid(memberUid);
                }
                FootballCarnivalVO.TeamInfo teamInfo = new FootballCarnivalVO.TeamInfo();
                teamInfo.setUid(memberUid);
                teamInfo.setName(actorData.getName());
                teamInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                String memberActivityId = getHashActivityId(activityId, memberUid);
                int trainNum = activityCommonRedis.getCommonHashValue(memberActivityId, String.format(KICK_STAGE_NUM_KEY, stageIndex));
                teamInfo.setScore(trainNum);
                teamMemberList.add(teamInfo);
            }
            teamInfoVO.setTeamMemberList(teamMemberList);
            int myTeamScore = activityCommonRedis.getCommonZSetRankingScore(getZSetTeamRankKey(activityId, stageIndex), teamId);
            teamInfoVO.setTeamScore(myTeamScore);
        }
        vo.setTeamInfo(teamInfoVO);
        if (stageIndex == 1) {
            vo.setIsJoinStage(1);
        } else if (stageIndex > 1) {
            if (ObjectUtils.isEmpty(teamId)) {
                vo.setIsJoinStage(0);
            } else {
                vo.setIsJoinStage(activityCommonRedis.isCommonSetData(getSetTeamPkKey(activityId, stageIndex), teamId));
            }
        }

        // 设置抽奖roll
        List<String> drawRecordList = activityCommonRedis.getCommonListRecord(getListScrollKey(activityId));
        List<FootballCarnivalVO.RollRecordData> rollRecordList = new ArrayList<>();
        for (String record : drawRecordList) {
            FootballCarnivalVO.RollRecordData rollRecordData = JSONObject.parseObject(record, FootballCarnivalVO.RollRecordData.class);
            ActorData actorData = actorDao.getActorDataFromCache(rollRecordData.getUid());
            rollRecordData.setName(actorData.getName());
            rollRecordData.setHead(actorData.getHead());
            rollRecordList.add(rollRecordData);
        }
        vo.setRollRecordList(rollRecordList);
        return vo;
    }

    public FootballCarnivalVO footballCarnivalTeamRank(String activityId, String uid) {
        FootballCarnivalVO vo = new FootballCarnivalVO();
        int stageIndex = getStageIndex(activityId, DateHelper.getNowSeconds());
        vo.setStage(stageIndex);

        String hashActivityId = getHashActivityId(activityId, uid);
        Map<String, String> userDataMap = activityCommonRedis.getCommonHashAllMapStr(hashActivityId);
        String myTeamId = userDataMap.get(TEAM_ID_KEY);

        // 设置榜单数据-海选阶段
        List<FootballCarnivalVO.FootballCarnivalTeamConfig> teamRankList = new ArrayList<>();
        Map<String, Integer> teamRankMap = activityCommonRedis.getCommonRankingMap(getZSetTeamRankKey(activityId, 1), 10);
        int rank = 1;
        for (Map.Entry<String, Integer> entry : teamRankMap.entrySet()) {
            String rankTeamId = entry.getKey();
            List<String> teamMemberUidList = activityCommonRedis.getCommonListRecord(getListTeamMemberKey(activityId, rankTeamId), 3);
            FootballCarnivalVO.FootballCarnivalTeamConfig FootballCarnivalTeamConfig = new FootballCarnivalVO.FootballCarnivalTeamConfig();
            List<FootballCarnivalVO.TeamInfo> teamMemberList = new ArrayList<>();
            for (int i = 0; i < teamMemberUidList.size(); i++) {
                String memberUid = teamMemberUidList.get(i);
                ActorData actorData = actorDao.getActorDataFromCache(memberUid);
                if (i == 0) {
                    FootballCarnivalTeamConfig.setTeamName(actorData.getName());
                    FootballCarnivalTeamConfig.setTeamLeadUid(memberUid);
                }
                FootballCarnivalVO.TeamInfo teamInfo = new FootballCarnivalVO.TeamInfo();
                teamInfo.setUid(memberUid);
                teamInfo.setName(actorData.getName());
                teamInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                teamMemberList.add(teamInfo);
            }
            FootballCarnivalTeamConfig.setTeamId(rankTeamId);
            FootballCarnivalTeamConfig.setTeamScore(entry.getValue());
            FootballCarnivalTeamConfig.setTeamMemberList(teamMemberList);
            FootballCarnivalTeamConfig.setRank(rank);
            teamRankList.add(FootballCarnivalTeamConfig);
            rank += 1;
        }
        vo.setTeamRankList(teamRankList);

        // 设置我的队伍数据-海选阶段有数据
        if (!ObjectUtils.isEmpty(myTeamId) && stageIndex == 1) {
            FootballCarnivalVO.FootballCarnivalTeamConfig myTeamConfig = new FootballCarnivalVO.FootballCarnivalTeamConfig();
            List<String> myTeamMemberUidList = activityCommonRedis.getCommonListRecord(getListTeamMemberKey(activityId, myTeamId), 3);
            List<FootballCarnivalVO.TeamInfo> teamMemberList = new ArrayList<>();
            for (int i = 0; i < myTeamMemberUidList.size(); i++) {
                String memberUid = myTeamMemberUidList.get(i);
                ActorData actorData = actorDao.getActorDataFromCache(memberUid);
                String memberActivityId = getHashActivityId(activityId, memberUid);
                Map<String, String> memberDataMap = memberUid.equals(uid) ? userDataMap : activityCommonRedis.getCommonHashAllMapStr(memberActivityId);
                int trainNum = Integer.parseInt(memberDataMap.getOrDefault(String.format(KICK_STAGE_NUM_KEY, stageIndex), "0"));
                if (i == 0) {
                    myTeamConfig.setTeamName(actorData.getName());
                    myTeamConfig.setTeamLeadUid(memberUid);
                }
                FootballCarnivalVO.TeamInfo teamInfo = new FootballCarnivalVO.TeamInfo();
                teamInfo.setUid(memberUid);
                teamInfo.setName(actorData.getName());
                teamInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                teamInfo.setScore(trainNum);
                teamMemberList.add(teamInfo);
            }
            myTeamConfig.setTeamId(myTeamId);
            int myTeamScore = activityCommonRedis.getCommonZSetRankingScore(getZSetTeamRankKey(activityId, stageIndex), myTeamId);
            myTeamConfig.setTeamScore(myTeamScore);
            myTeamConfig.setTeamMemberList(teamMemberList);

            int myRank = activityCommonRedis.getCommonZSetRank(getZSetTeamRankKey(activityId, stageIndex), myTeamId);
            myTeamConfig.setRank(myRank);

            // 处理距离上一名分差
            int muchScore = 0;
            int lastRank = myRank - 1;
            if (lastRank > 0) {
                // 只获取上一名的数据
                Map<String, Integer> lastRankMap = activityCommonRedis.getCommonRankingMapByPage(getZSetTeamRankKey(activityId, stageIndex), lastRank - 1, lastRank);
                if (!CollectionUtils.isEmpty(lastRankMap)) {
                    // 只取第一个元素，即上一名的分数
                    for (Map.Entry<String, Integer> entry : lastRankMap.entrySet()) {
                        int lastScore = entry.getValue();
                        logger.info("footballCarnivalTeamRank lastRankMap rankTeamId:{} lastScore:{} myTeamScore:{}", entry.getKey(), lastScore, myTeamScore);
                        // 计算分差：上一名分数 - 我的分数
                        muchScore = lastScore - myTeamScore;
                        break; // 只处理第一个元素
                    }
                }
            }
            myTeamConfig.setMuchScore(muchScore);
            vo.setTeamInfo(myTeamConfig);
        }

        // 设置pk赛数据
        List<FootballCarnivalVO.FootballCarnivalTeamCompetitionRoundVO> roundRankList = new ArrayList<>();
        for (int i = 2; i <= STAGE_START_TIME_LIST.size(); i++) {
            FootballCarnivalVO.FootballCarnivalTeamCompetitionRoundVO roundRankVO = new FootballCarnivalVO.FootballCarnivalTeamCompetitionRoundVO();
            roundRankVO.setStage(i);
            List<FootballCarnivalVO.FootballCarnivalTeamCompetitionVO> pkRankList = buildPkRankList(activityId, i, myTeamId, stageIndex);
            roundRankVO.setPkRankList(pkRankList);
            roundRankList.add(roundRankVO);
        }
        vo.setRoundRankList(roundRankList);

        return vo;
    }

    /**
     * 构建PK赛对阵列表
     *
     * @param activityId        活动ID
     * @param stageIndex        阶段索引 (2,3,4轮pk赛)
     * @param myTeamId          我的队伍ID
     * @param currentStageIndex 当前阶段索引
     * @return PK对阵列表
     */
    private List<FootballCarnivalVO.FootballCarnivalTeamCompetitionVO> buildPkRankList(String activityId, int stageIndex, String myTeamId, int currentStageIndex) {
        List<FootballCarnivalVO.FootballCarnivalTeamCompetitionVO> pkRankList = new ArrayList<>();

        // 从Redis获取该阶段的PK对阵数据
        String hashPkActivityId = getHashPkActivityId(activityId);
        Map<String, String> hashAllMap = activityCommonRedis.getCommonHashAllMapStr(hashPkActivityId);
        String roundStrPKData = hashAllMap.get(String.valueOf(stageIndex));

        if (StringUtils.isEmpty(roundStrPKData)) {
            return pkRankList;
        }

        List<String> pkDataList = JSONObject.parseObject(roundStrPKData, List.class);
        if (CollectionUtils.isEmpty(pkDataList)) {
            return pkRankList;
        }

        // 查找我的队伍的对阵，如果存在则放在第一位
        FootballCarnivalVO.FootballCarnivalTeamCompetitionVO myTeamCompetition = null;
        List<FootballCarnivalVO.FootballCarnivalTeamCompetitionVO> otherCompetitions = new ArrayList<>();

        for (String pkData : pkDataList) {
            FootballCarnivalVO.FootballCarnivalTeamCompetitionVO competition = parsePkData(activityId, pkData, stageIndex, myTeamId, currentStageIndex);
            if (competition != null) {
                // 检查是否包含我的队伍
                boolean containsMyTeam = false;
                if (competition.getTeam1() != null && myTeamId != null && myTeamId.equals(competition.getTeam1().getTeamId())) {
                    containsMyTeam = true;
                } else if (competition.getTeam2() != null && myTeamId != null && myTeamId.equals(competition.getTeam2().getTeamId())) {
                    containsMyTeam = true;
                }

                if (containsMyTeam) {
                    myTeamCompetition = competition;
                } else {
                    otherCompetitions.add(competition);
                }
            }
        }

        // 如果我的队伍进入了pk赛，则放在第一位
        if (myTeamCompetition != null) {
            pkRankList.add(myTeamCompetition);
        }
        pkRankList.addAll(otherCompetitions);

        return pkRankList;
    }

    /**
     * 解析PK数据并构建团队对阵信息
     *
     * @param activityId        活动ID
     * @param pkData            PK数据字符串，格式：teamId1_rank1-teamId2_rank2
     * @param stageIndex        阶段索引
     * @param myTeamId          我的队伍ID
     * @param currentStageIndex 当前阶段索引
     * @return 团队对阵信息
     */
    private FootballCarnivalVO.FootballCarnivalTeamCompetitionVO parsePkData(String activityId, String pkData, int stageIndex, String myTeamId, int currentStageIndex) {
        try {
            // 解析PK数据格式：teamId1_rank1-teamId2_rank2
            String[] teams = pkData.split("-");
            if (teams.length != 2) {
                logger.error("parsePkData invalid format: {}", pkData);
                return null;
            }

            String[] team1Data = teams[0].split("_");
            String[] team2Data = teams[1].split("_");

            if (team1Data.length != 2 || team2Data.length != 2) {
                logger.error("parsePkData invalid team data format: {}", pkData);
                return null;
            }

            String team1Id = team1Data[0];
            String team2Id = team2Data[0];

            // 构建团队信息
            FootballCarnivalVO.FootballCarnivalTeamConfig team1Config = buildTeamConfig(activityId, team1Id, stageIndex, myTeamId, currentStageIndex);
            FootballCarnivalVO.FootballCarnivalTeamConfig team2Config = buildTeamConfig(activityId, team2Id, stageIndex, myTeamId, currentStageIndex);

            if (team1Config == null || team2Config == null) {
                logger.error("parsePkData failed to build team config for: {}", pkData);
                return null;
            }

            FootballCarnivalVO.FootballCarnivalTeamCompetitionVO competition = new FootballCarnivalVO.FootballCarnivalTeamCompetitionVO();
            competition.setTeam1(team1Config);
            competition.setTeam2(team2Config);

            return competition;
        } catch (Exception e) {
            logger.error("parsePkData error for pkData: {}", pkData, e);
            return null;
        }
    }

    /**
     * 构建团队配置信息
     *
     * @param activityId        活动ID
     * @param teamId            队伍ID
     * @param stageIndex        阶段索引
     * @param myTeamId          我的队伍ID
     * @param currentStageIndex 当前阶段索引
     * @return 团队配置信息
     */
    private FootballCarnivalVO.FootballCarnivalTeamConfig buildTeamConfig(String activityId, String teamId, int stageIndex, String myTeamId, int currentStageIndex) {
        try {
            // 获取团队成员列表
            List<String> teamMemberUidList = activityCommonRedis.getCommonListRecord(getListTeamMemberKey(activityId, teamId), 3);
            if (CollectionUtils.isEmpty(teamMemberUidList)) {
                logger.error("buildTeamConfig no team members found for teamId: {}", teamId);
                return null;
            }

            FootballCarnivalVO.FootballCarnivalTeamConfig teamConfig = new FootballCarnivalVO.FootballCarnivalTeamConfig();
            teamConfig.setTeamId(teamId);

            // 获取团队分数
            String teamRankKey = getZSetTeamRankKey(activityId, stageIndex);
            Integer teamScore = activityCommonRedis.getCommonZSetRankingScore(teamRankKey, teamId);
            teamConfig.setTeamScore(teamScore != null ? teamScore : 0);
            Integer rank = activityCommonRedis.getCommonZSetRank(teamRankKey, teamId);
            rank = rank == 0 ? 99 : rank;
            teamConfig.setRank(rank);

            // 构建团队成员信息
            List<FootballCarnivalVO.TeamInfo> teamMemberList = new ArrayList<>();
            for (int i = 0; i < teamMemberUidList.size(); i++) {
                String memberUid = teamMemberUidList.get(i);
                ActorData actorData = actorDao.getActorDataFromCache(memberUid);
                if (actorData == null) {
                    continue;
                }

                if (i == 0) {
                    // 第一个成员是队长
                    teamConfig.setTeamName(actorData.getName());
                    teamConfig.setTeamLeadUid(memberUid);
                }

                FootballCarnivalVO.TeamInfo teamInfo = new FootballCarnivalVO.TeamInfo();
                teamInfo.setUid(memberUid);
                teamInfo.setName(actorData.getName());
                teamInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));

                String memberActivityId = getHashActivityId(activityId, memberUid);
                int trainNum = activityCommonRedis.getCommonHashValue(memberActivityId, String.format(KICK_STAGE_NUM_KEY, stageIndex));
                teamInfo.setScore(trainNum);
                teamMemberList.add(teamInfo);
            }
            teamConfig.setTeamMemberList(teamMemberList);

            // 计算muchPkScore：仅在自己队伍进入pk赛，且当前stageIndex与i值相同时赋值
            if (myTeamId != null && myTeamId.equals(teamId) && currentStageIndex == stageIndex) {
                int muchPkScore = calculateMuchPkScore(activityId, teamId, stageIndex);
                teamConfig.setMuchPkScore(muchPkScore);
            }

            return teamConfig;
        } catch (Exception e) {
            logger.error("buildTeamConfig error for teamId: {}", teamId, e);
            return null;
        }
    }

    /**
     * 计算PK分数差
     *
     * @param activityId 活动ID
     * @param myTeamId   我的队伍ID
     * @param stageIndex 阶段索引
     * @return muchPkScore 如果自己的队伍比对手分数高返回-1，否则返回正的差值
     */
    private int calculateMuchPkScore(String activityId, String myTeamId, int stageIndex) {
        try {
            // 获取该阶段的PK对阵数据
            String hashPkActivityId = getHashPkActivityId(activityId);
            Map<String, String> hashAllMap = activityCommonRedis.getCommonHashAllMapStr(hashPkActivityId);
            String roundStrPKData = hashAllMap.get(String.valueOf(stageIndex));

            if (StringUtils.isEmpty(roundStrPKData)) {
                return 0;
            }

            List<String> pkDataList = JSONObject.parseObject(roundStrPKData, List.class);
            if (CollectionUtils.isEmpty(pkDataList)) {
                return 0;
            }

            // 查找我的队伍的对阵
            for (String pkData : pkDataList) {
                String[] teams = pkData.split("-");
                if (teams.length != 2) {
                    continue;
                }

                String[] team1Data = teams[0].split("_");
                String[] team2Data = teams[1].split("_");

                if (team1Data.length != 2 || team2Data.length != 2) {
                    continue;
                }

                String team1Id = team1Data[0];
                String team2Id = team2Data[0];

                String opponentTeamId = null;
                if (myTeamId.equals(team1Id)) {
                    opponentTeamId = team2Id;
                } else if (myTeamId.equals(team2Id)) {
                    opponentTeamId = team1Id;
                }

                if (opponentTeamId != null) {
                    // 找到对手，计算分数差
                    String teamRankKey = getZSetTeamRankKey(activityId, stageIndex);
                    Integer myTeamScore = activityCommonRedis.getCommonZSetRankingScore(teamRankKey, myTeamId);
                    Integer opponentScore = activityCommonRedis.getCommonZSetRankingScore(teamRankKey, opponentTeamId);

                    int myScore = myTeamScore != null ? myTeamScore : 0;
                    int oppScore = opponentScore != null ? opponentScore : 0;

                    // 如果自己的队伍比对手分数高，返回-1；否则返回正的差值
                    if (myScore > oppScore) {
                        return -1;
                    } else {
                        return oppScore - myScore;
                    }
                }
            }

            return 0;
        } catch (Exception e) {
            logger.error("calculateMuchPkScore error for teamId: {}", myTeamId, e);
            return 0;
        }
    }

    /**
     * 礼物发送处理
     */
    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        String fromUid = giftData.getFrom_uid();
        int giftId = giftData.getGid();
        int number = giftData.getNumber();
//        synchronized (stringPool.intern(getLocalKey(fromUid))) {
            int totalBeans = number * giftData.getPrice() * giftData.getAid_list().size();
            String hashActivityId = getHashActivityId(activityId, fromUid);
            Map<String, String> userDataMap = activityCommonRedis.getCommonHashAllMapStr(hashActivityId);
            int leftBean = Integer.parseInt(userDataMap.getOrDefault(LEFT_BEAN_NUM_KEY, "0"));
            totalBeans += leftBean;
            int incNum = totalBeans / 100;
            leftBean = totalBeans % 100;
            if (incNum > 0) {
                activityCommonRedis.incCommonHashNum(hashActivityId, CHANCE_NUM_KEY, incNum);
                doReportSpecialItemsEvent(activityId, fromUid, 1, incNum, 1);
            }
            activityCommonRedis.setCommonHashNum(hashActivityId, LEFT_BEAN_NUM_KEY, leftBean);
//        }
        if (SPECIAL_GIFT_ID == giftId) {
            for (String aid : giftData.getAid_list()) {
                // String hashActivityId = getHashActivityId(activityId, aid);
                // if (activityCommonRedis.getCommonHashValue(hashActivityId, SPECIAL_GIFT_RECEIVE_KEY) == 0) {
                for (int num = 0; num < number; num++) {
                    resourceKeyHandlerService.sendResourceData(aid, SPECIAL_GIFT_RECEIVE_RES_KEY, "Football Carnival-SpecialGift", "Football Carnival-SpecialGift");
                }
                // activityCommonRedis.setCommonHashNum(hashActivityId, SPECIAL_GIFT_RECEIVE_KEY, 1);
                // }
            }
        }
    }

    /**
     * 抽奖
     *
     * @param activityId: 活动id
     * @param uid:        用户uid
     * @param amount:     次数
     * @param goal:       1 进球 0 未进球
     * @return vo
     */

    public FootballCarnivalVO footballCarnivalDraw(String activityId, String uid, int amount, int goal) {
        checkActivityTime(activityId);
        if (!DRAW_NUM_LIST.contains(amount)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        FootballCarnivalVO vo = new FootballCarnivalVO();
        synchronized (stringPool.intern(getTeamJoinLocalKey(uid))) { // 锁可能存在作为队长有人在加入，自己又正在玩游戏，myTeamId不同步问题，导致个人数据写了第二个阶段，而队伍数据只增加到了第一个阶段
            String hashActivityId = getHashActivityId(activityId, uid);
            Map<String, String> userDataMap = activityCommonRedis.getCommonHashAllMapStr(hashActivityId);
            int stoneNum = Integer.parseInt(userDataMap.getOrDefault(CHANCE_NUM_KEY, "0"));
            if (stoneNum <= 0 || stoneNum < amount) {
                throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER);
            }
            int stageIndex = getStageIndex(activityId, DateHelper.getNowSeconds());
            String kickTotalNumKey = String.format(KICK_STAGE_NUM_KEY, stageIndex);
            // int trainDragonNum = Integer.parseInt(userDataMap.getOrDefault(kickTotalNumKey, "0"));

            // 扣抽奖次数
            activityCommonRedis.incCommonHashNum(hashActivityId, CHANCE_NUM_KEY, -amount);
            doReportSpecialItemsEvent(activityId, uid, 2, amount, 2);
            // 增加个人总踢球次数
            activityCommonRedis.incCommonHashNum(hashActivityId, KICK_TOTAL_NUM_KEY, amount);

            // 增加团队踢球次数
            String myTeamId = userDataMap.get(TEAM_ID_KEY);
            if (!ObjectUtils.isEmpty(myTeamId)) {
                if (this.incTeamTrainScore(activityId, myTeamId,stageIndex, amount, amount, goal > 0 ? amount : 0)) {
                    // 增加踢球次数-阶段数据 队伍增加数据成功了再加个人的
                    activityCommonRedis.incCommonHashNum(hashActivityId, kickTotalNumKey, amount);
                }
            } else {
                // 增加踢球次数-阶段数据
                activityCommonRedis.incCommonHashNum(hashActivityId, kickTotalNumKey, amount);
            }


            int currentTime = DateHelper.getNowSeconds();
            if (goal > 0) {
                activityCommonRedis.incCommonHashNum(hashActivityId, KICK_SUCCESS_TOTAL_NUM_KEY, amount);
                // 最近30天充值金额
                int rechargeMoney = rechargeDailyInfoDao.getUserLastRechargeCache(uid, 30);
                List<FootballCarnivalVO.RollRecordData> drawRecordList = new ArrayList<>();
                ResourceKeyConfigData resourceKeyConfigData = this.getResourceKeyConfig(rechargeMoney, 2);
                if (resourceKeyConfigData == null) {
                    logger.error("footballCarnivalDraw not find resourceKeyConfigData:{}", rechargeMoney);
                    throw new CommonH5Exception(ActivityHttpCode.SERVER_ERROR);
                }
                for (int i = 0; i < amount; i++) {
                    // trainDragonNum += 1;
                    // int currentLevel = getCurrLevelScore(trainDragonNum);

                    String resKey = resourceKeyConfigData.getKey();
                    String rewardTitle = this.getResTitleByKey(resKey);

                    ResourceKeyConfigData.ResourceMeta resourceMeta = drawOne(uid, resKey, rewardTitle);
                    if (resourceMeta == null) {
                        continue;
                    }
                    // resourceKeyHandlerService.sendOneResourceData(actorData, resourceMeta, 905, rewardTitle, rewardTitle, rewardTitle, "", "", 0);
                    FootballCarnivalVO.RollRecordData drawRecord = new FootballCarnivalVO.RollRecordData();
                    BeanUtils.copyProperties(resourceMeta, drawRecord);
                    drawRecord.setUid(uid);
                    drawRecord.setCtime(currentTime);
                    drawRecordList.add(drawRecord);

                    String jsonRecord = JSON.toJSONString(drawRecord);
                    activityCommonRedis.addCommonListRecord(getListScrollKey(activityId), jsonRecord);
                    activityCommonRedis.addCommonListData(getListHistoryDrawKey(activityId, uid), jsonRecord);
                    doDrawPrizeRecordEvent(uid, getSenceDetail(resKey), 1, resourceMeta,1);
                }
                vo.setRecordList(drawRecordList);
            } else {
                FootballCarnivalVO.RollRecordData drawRecord = new FootballCarnivalVO.RollRecordData();
                drawRecord.setUid(uid);
                drawRecord.setCtime(currentTime);
                drawRecord.setResourceType(NO_GOAL_TYPE);

                String jsonRecord = JSON.toJSONString(drawRecord);
                activityCommonRedis.addCommonListData(getListHistoryDrawKey(activityId, uid), jsonRecord);
                // 最近30天充值金额
                int rechargeMoney = rechargeDailyInfoDao.getUserLastRechargeCache(uid, 30);
                int senceDetail = rechargeMoney >= 5 ? 1 : 2;
                for (int i = 0; i < amount; i++) {
                    doDrawPrizeRecordEvent(uid, senceDetail, 1, drawRecord,0);
                }

            }
        }
        return vo;
    }

    /**
     * 增加团队分数
     */
    private boolean incTeamTrainScore(String activityId, String teamId, int stageIndex,int stageValue, int value, int goalValue) {
//        int stageIndex = getStageIndex(activityId, DateHelper.getNowSeconds());
        String teamRankKey = getZSetTeamRankKey(activityId, stageIndex);
        boolean stageAddSuccess = false;
        synchronized (stringPool.intern(getTeamLocalKey(teamId))) {
            if (stageValue > 0) {
                // 队伍阶段排行榜数据
                if (stageIndex == 1) {
                    activityCommonRedis.incrCommonZSetRankingScore(teamRankKey, teamId, stageValue);
                    stageAddSuccess = true;
                } else {
                    Set<String> allPkTeamIdSet = cacheDataService.getAllSetCache(getSetTeamPkKey(activityId, stageIndex), 2);
                    if (allPkTeamIdSet.contains(teamId)) {
                        // 只统计进入PK对阵
                        activityCommonRedis.incrCommonZSetRankingScore(teamRankKey, teamId, stageValue);
                        stageAddSuccess = true;
                    } else {
                        logger.info("incTeamTrainScore not in pk teamId:{} stageIndex:{} stageValue:{}", teamId, stageIndex, stageValue);
                    }
                }
            }

            String teamHashActivityId = getHashActivityIdTeam(activityId, teamId);

            Map<String, Integer> oldHashMap = activityCommonRedis.getCommonHashAll(teamHashActivityId);
            if (value > 0) {
                int kickTotalNum = activityCommonRedis.incCommonHashNum(teamHashActivityId, TEAM_KICK_TOTAL_NUM_KEY, value);
                if (kickTotalNum >= TEAM_TOTAL_KICK_BALL_NUM_REACH && oldHashMap.getOrDefault(TEAM_KICK_TOTAL_NUM_KEY, 0) < TEAM_TOTAL_KICK_BALL_NUM_REACH) {
                    doTeamReward(activityId, teamId, TEAM_TASK_TOTAL_KICK);
                }

                if (kickTotalNum >= TEAM_TOTAL_KICK_BALL_2_NUM_REACH && oldHashMap.getOrDefault(TEAM_KICK_TOTAL_NUM_KEY, 0) < TEAM_TOTAL_KICK_BALL_2_NUM_REACH) {
                    doTeamReward(activityId, teamId, TEAM_TASK_TOTAL_KICK_2);
                }
            }
            if (goalValue > 0) {
                int kickSuccessTotalNum = activityCommonRedis.incCommonHashNum(teamHashActivityId, TEAM_KICK_SUCCESS_TOTAL_NUM_KEY, goalValue);
                // if (kickSuccessTotalNum >= TEAM_TOTAL_GOAL_NUM_REACH && oldHashMap.getOrDefault(TEAM_KICK_SUCCESS_TOTAL_NUM_KEY,0) < TEAM_TOTAL_GOAL_NUM_REACH) {
                //     doTeamReward(activityId, teamId, TEAM_TASK_TOTAL_GOAL);
                // }
            }
        }
        return stageAddSuccess;
    }

    private void doTeamReward(String activityId, String teamId, String resKey) {
        List<String> teamMemberUidList = activityCommonRedis.getCommonListRecord(getListTeamMemberKey(activityId, teamId), 3);
        for (String memberUid : teamMemberUidList) {
            resourceKeyHandlerService.sendResourceData(memberUid, resKey, "Football Carnival-TeamTaskreward", "Football Carnival-TeamTaskreward");
        }
    }


    /**
     * 获取抽奖配置
     */
    private ResourceKeyConfigData getResourceKeyConfig(int rechargeMoney, int level) {
        return rechargeMoney >= 5 ? resourceKeyHandlerService.getConfigData(POOL_SIZE_PAY_KEY) : resourceKeyHandlerService.getConfigData(POOL_SIZE_NO_PAY_KEY);
    }

    /**
     * 获取中奖title
     */
    private String getResTitleByKey(String resKey) {
        if (POOL_SIZE_PAY_KEY.equals(resKey)) {
            return "Football Carnival-reward2";
        } else {
            return "Football Carnival-reward3";
        }
    }

    /**
     * 获取抽奖SenceDetail
     */
    private int getSenceDetail(String resKey) {
        if (POOL_SIZE_PAY_KEY.equals(resKey)) {
            return 1;
        } else {
            return 2;
        }
    }

    /**
     * 使用卡片抽奖
     */
    private void initDrawPrizePool(Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap, String drawPrizeKey) {
        int poolSize = activityCommonRedis.getCommonListSize(drawPrizeKey);
        if (poolSize <= LIMIT_INIT_POOL) {
            List<String> poolList = new ArrayList<>();
            for (String prizeKey : resourceMetaMap.keySet()) {
                ResourceKeyConfigData.ResourceMeta resourceMeta = resourceMetaMap.get(prizeKey);
                int rateNumber = Integer.parseInt(resourceMeta.getRateNumber());
                poolList.addAll(Stream.generate(resourceMeta::getMetaId).limit(rateNumber).collect(Collectors.toList()));
            }
            Collections.shuffle(poolList);
            activityCommonRedis.rightPushAllCommonList(drawPrizeKey, poolList);
        }
    }

    /**
     * 不放回抽奖
     */
    // private String FootballCarnivalDrawSize(String activityId, Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap, String resKey) {
    //     String drawPrizeKey = getListDrawPrizeKey(activityId, resKey);
    //     this.initDrawPrizePool(resourceMetaMap, drawPrizeKey);
    //     String awardKey = activityCommonRedis.leftPopCommonListKey(drawPrizeKey);
    //     if (StringUtils.isEmpty(awardKey)) {
    //         throw new CommonH5Exception(HttpCode.SERVER_ERROR);
    //     }
    //     return awardKey;
    // }

    /**
     * 历史记录
     */
    public FootballCarnivalVO footballCarnivalRecord(String activityId, String uid, int page) {
        FootballCarnivalVO vo = new FootballCarnivalVO();
        int start = (page - 1) * RECORD_PAGE_SIZE;
        int end = page * RECORD_PAGE_SIZE;

        List<String> pageRecordList = activityCommonRedis.getCommonListPageRecord(getListHistoryDrawKey(activityId, uid), start, end);
        List<FootballCarnivalVO.RollRecordData> recordList = new ArrayList<>();
        for (String record : pageRecordList) {
            FootballCarnivalVO.RollRecordData rollRecordData = JSONObject.parseObject(record, FootballCarnivalVO.RollRecordData.class);
            ActorData actorData = actorDao.getActorDataFromCache(rollRecordData.getUid());
            rollRecordData.setName(actorData.getName());
            rollRecordData.setHead(actorData.getHead());
            recordList.add(rollRecordData);
        }
        vo.setRecordList(recordList);
        if (pageRecordList.size() < RECORD_PAGE_SIZE) {
            vo.setNextUrl(0);
        } else {
            vo.setNextUrl(page + 1);
        }
        return vo;
    }

    /**
     * 组队
     */
    public void footballCarnivalJoinTeam(String activityId, String uid, String captainUid) {
        checkActivityTime(activityId);
        synchronized (stringPool.intern(getTeamJoinLocalKey(captainUid))) {
            if (ObjectUtils.isEmpty(captainUid)) {
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }

            if (captainUid.equals(uid)) {
                throw new CommonH5Exception(ActivityHttpCode.QUEEN_ALREADY_LIKE.getCode(), "لا تستطيع التعاون مع نفسك");
            }

            ActorData captainActorData = actorDao.getActorDataFromCache(captainUid);
            // 队长不存在
            if (captainActorData == null) {
                throw new CommonH5Exception(ActivityHttpCode.QUEEN_NOT_DIAMONDS.getCode(), "المستخدم الكابتن الذي تقدمت بطلب للانضمام إليه غير موجود");
            }

            String hashActivityId = getHashActivityId(activityId, uid);
            Map<String, String> userDataMap = activityCommonRedis.getCommonHashAllMapStr(hashActivityId);
            String teamId = userDataMap.get(TEAM_ID_KEY);
            int stageIndex = getStageIndex(activityId, DateHelper.getNowSeconds());
            String kickTotalNumKey = String.format(KICK_STAGE_NUM_KEY, stageIndex);
            // 阶段数据
            int trainNum = Integer.parseInt(userDataMap.getOrDefault(kickTotalNumKey, "0"));
            // 总数据
            int kickTotalNum = Integer.parseInt(userDataMap.getOrDefault(KICK_TOTAL_NUM_KEY, "0"));
            // 总进球数
            int kickSuccessTotalNum = Integer.parseInt(userDataMap.getOrDefault(KICK_SUCCESS_TOTAL_NUM_KEY, "0"));

            // 判断该用户是否已加入队伍
            if (!ObjectUtils.isEmpty(teamId)) {
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "لقد انضممت إلى فريق آخر");
            }

            String captainHashActivityId = getHashActivityId(activityId, captainUid);
            Map<String, String> captainDataMap = activityCommonRedis.getCommonHashAllMapStr(captainHashActivityId);
            String captainTeamId = captainDataMap.get(TEAM_ID_KEY);
            // 队长没有团队id, 则加入
            if (ObjectUtils.isEmpty(captainTeamId)) {
                captainTeamId = new ObjectId().toString();
                List<String> teamMemberList = new ArrayList<>();
                teamMemberList.add(captainUid);
                teamMemberList.add(uid);

                activityCommonRedis.setCommonHashData(captainHashActivityId, TEAM_ID_KEY, captainTeamId);
                activityCommonRedis.setCommonHashData(hashActivityId, TEAM_ID_KEY, captainTeamId);
                activityCommonRedis.rightPushAllCommonList(getListTeamMemberKey(activityId, captainTeamId), teamMemberList);
                // 处理团队分数及奖励-阶段数据
                int captainTrainNum = Integer.parseInt(captainDataMap.getOrDefault(kickTotalNumKey, "0"));
                int totalScore = trainNum + captainTrainNum;

                int captainKickTotalNum = Integer.parseInt(captainDataMap.getOrDefault(KICK_TOTAL_NUM_KEY, "0"));
                int totalKickNum = kickTotalNum + captainKickTotalNum;

                int captainKickSuccessTotalNum = Integer.parseInt(captainDataMap.getOrDefault(KICK_SUCCESS_TOTAL_NUM_KEY, "0"));
                int totalKickSuccessNum = kickSuccessTotalNum + captainKickSuccessTotalNum;


                this.incTeamTrainScore(activityId, captainTeamId,stageIndex, totalScore, totalKickNum, totalKickSuccessNum);
                doTeamRecordEvent(activityId, uid, captainUid, captainActorData.getName());
                doTeamRecordEvent(activityId, captainUid, captainUid, captainActorData.getName());
                logger.info("FootballCarnivalJoinTeam1 captainTeamId:{}, captainUid:{}, uid:{}, totalScore:{} totalKickNum:{} totalKickSuccessNum:{}",
                        captainTeamId, captainUid, uid, totalScore, totalKickNum, totalKickSuccessNum);
            } else {
                String teamMemberKey = getListTeamMemberKey(activityId, captainTeamId);
                List<String> teamMemberUidList = activityCommonRedis.getCommonListRecord(teamMemberKey, 3);
                if (CollectionUtils.isEmpty(teamMemberUidList)) {
                    throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "الفريق الذي طلبت الانضمام إليه غير موجود");
                }
                if (teamMemberUidList.size() >= 3) {
                    throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "الفريق الذي تتقدم بطلب الانضمام إليه ممتلئ");
                }
                String originCaptainUid = teamMemberUidList.get(0);
                teamMemberUidList.add(uid);
                activityCommonRedis.setCommonHashData(hashActivityId, TEAM_ID_KEY, captainTeamId);
                activityCommonRedis.addRightCommonListData(teamMemberKey, uid);
                this.incTeamTrainScore(activityId, captainTeamId,stageIndex, trainNum, kickTotalNum, kickSuccessTotalNum);
                doTeamRecordEvent(activityId, uid, originCaptainUid, captainActorData.getName());
                logger.info("FootballCarnivalJoinTeam2 captainTeamId:{}, captainUid:{}, uid:{}, totalScore:{} totalKickNum:{} totalKickSuccessNum:{}",
                        captainTeamId, originCaptainUid, uid, trainNum, kickTotalNum, kickSuccessTotalNum);
                if (teamMemberUidList.size() == 3) {
                    for (String memberUid : teamMemberUidList) {
                        resourceKeyHandlerService.sendResourceData(memberUid, TEAM_TASK_MEMBER_REACH, "Football Carnival-Teamreward", "Football Carnival-Teamreward");
                    }
                }
            }
        }
    }

    /**
     * 活动阶段结束，每小时执行一次
     *
     * @param activityId
     */
    public void handleStageOver(String activityId) {
        if (getOtherRankingActivityNull(activityId) == null) {
            return;
        }

        if (!inActivityTime(activityId)) {
            return;
        }
        int stageIndex = getStageIndex(activityId, DateHelper.getNowSeconds());
        if (stageIndex <= 1) {
            return;
        }
        if (stageIndex == 2) {
            // 处理晋级赛数据 生成8->4对阵pk
            handleRound1TopRankData(activityId, stageIndex);
        } else {
            // stageIndex值为3,4 处理pk赛数据 生成4->2 2->1对阵pk
            handleRoundPkData(activityId, stageIndex);
        }

    }

    /**
     * 晋级赛到pk赛
     *
     * @param activityId
     */
    public void handleRound1TopRankData(String activityId, int stageIndex) {
        String hashPkActivityId = getHashPkActivityId(activityId);
        String stageOverKey = String.format(STAGE_OVER_KEY, stageIndex);
        if (activityCommonRedis.getCommonHashValue(hashPkActivityId, stageOverKey) == 1) {
            // logger.info("handleRound1TopRankData already handle stageOverKey:{} activityId:{}", stageOverKey, activityId);
            return;
        }

        List<String> top8RankList = activityCommonRedis.getCommonRankingList(getZSetTeamRankKey(activityId, 1), 8);
        int rank = 1;
        Map<String, Integer> uidRankMap = new HashMap<>();
        for (String aid : top8RankList) {
            uidRankMap.put(aid, rank);
            rank += 1;
        }
        int listSize = uidRankMap.size();
        if (listSize < 2) {
            logger.error("handleMissRound1TopRankData listSize < 2 activityId:{}", activityId);
            return;
        }
        int endListIndex = listSize - 1;
        int halfLength = listSize / 2;
        List<String> missPkRankList = new ArrayList<>();
        Set<String> missPkUidSet = new HashSet<>();
        for (int i = 0; i < halfLength; i++) {
            String player1 = top8RankList.get(i);
            String player2 = top8RankList.get(endListIndex - i);
            int player1Rank = uidRankMap.getOrDefault(player1, 0);
            int player2Rank = uidRankMap.getOrDefault(player2, 0);
            String playUser = String.format("%s_%s-%s_%s", player1, player1Rank, player2, player2Rank);
            missPkUidSet.add(player1);
            missPkUidSet.add(player2);
            missPkRankList.add(playUser);
        }
        activityCommonRedis.setCommonHashData(hashPkActivityId, String.valueOf(stageIndex), JSONObject.toJSONString(missPkRankList));
        activityCommonRedis.addSetValues(getSetTeamPkKey(activityId, stageIndex), missPkUidSet);
        activityCommonRedis.setCommonHashNum(hashPkActivityId, stageOverKey, 1);
        cacheDataService.delSetCache(getSetTeamPkKey(activityId, stageIndex), 2);
    }

    /**
     * pk赛 8->4  4->2  2->1
     *
     * @param activityId
     */
    public void handleRoundPkData(String activityId, int stageIndex) {
        int preStageIndex = stageIndex - 1;
        if (preStageIndex < 1) {
            logger.error("handleRoundPkData preStageIndex < 1 activityId:{}", activityId);
            return;
        }
        String hashPkActivityId = getHashPkActivityId(activityId);
        String stageOverKey = String.format(STAGE_OVER_KEY, stageIndex);
        if (activityCommonRedis.getCommonHashValue(hashPkActivityId, stageOverKey) == 1) {
            // logger.info("handleRoundPkData already handle stageOverKey:{} activityId:{}", stageOverKey, activityId);
            return;
        }

        Map<String, String> hashAllMap = activityCommonRedis.getCommonHashAllMapStr(getHashPkActivityId(activityId));
        String roundStrPKData = hashAllMap.get(String.valueOf(preStageIndex));

        if (!StringUtils.isEmpty(roundStrPKData)) {
            List<String> missPKList = JSONObject.parseObject(roundStrPKData, List.class);
            int pkListSize = missPKList.size();
            if (pkListSize < 2) {
                logger.error("handleMissRoundPkData pkListSize < 2 activityId:{}", activityId);
                return;
            }

            List<Integer> rankWinList = new ArrayList<>();
            List<String> rankLoseList = new ArrayList<>();
            Map<Integer, String> rankWinMap = new HashMap<>();

            int defaultIndex = 1;
            String teamRankKey = getZSetTeamRankKey(activityId, preStageIndex);
            for (String pkUser : missPKList) {
                String[] pkUserList = pkUser.split("-");
                String[] pk1UidRank = pkUserList[0].split("_");
                String[] pk2UidRank = pkUserList[1].split("_");
                String pk1Uid = pk1UidRank[0];
                String pk2Uid = pk2UidRank[0];
                int rank1 = activityCommonRedis.getCommonZSetRank(teamRankKey, pk1Uid);
                rank1 = rank1 == 0 ? 99 : rank1;
                int rank2 = activityCommonRedis.getCommonZSetRank(teamRankKey, pk2Uid);
                rank2 = rank2 == 0 ? 99 : rank2;

                String winUid = rank1 <= rank2 ? pk1Uid : pk2Uid;
                String loseUid = winUid.equals(pk1Uid) ? pk2Uid : pk1Uid;
                rankLoseList.add(loseUid);

                int winRank = Math.min(rank1, rank2);
                winRank = winRank == 99 ? winRank + defaultIndex : winRank;

                defaultIndex += 1;

                rankWinList.add(winRank);
                rankWinMap.put(winRank, winUid);

            }
            Collections.sort(rankWinList);

            // 发放奖励
            int rank = 1;
            Map<String, Integer> uidRankMap = new HashMap<>();
            for (Integer winRank : rankWinList) {
                String winUid = rankWinMap.get(winRank);
                if (winUid != null) {
                    uidRankMap.put(winUid, rank);
                }
                rank += 1;
            }

            // 新一轮的对阵形式
            List<String> pkRankStrList = new ArrayList<>();
            Set<String> missPkUidSet = new HashSet<>();
            int listSize = rankWinList.size();
            int endListIndex = listSize - 1;
            int halfLength = listSize / 2;
            for (int i = 0; i < halfLength; i++) {
                int winRank1 = rankWinList.get(i);
                int winRank2 = rankWinList.get(endListIndex - i);
                String win1Uid = rankWinMap.get(winRank1);
                String win2Uid = rankWinMap.get(winRank2);
                int player1Rank = uidRankMap.getOrDefault(win1Uid, 0);
                int player2Rank = uidRankMap.getOrDefault(win2Uid, 0);
                String playUser = String.format("%s_%s-%s_%s", win1Uid, player1Rank, win2Uid, player2Rank);
                pkRankStrList.add(playUser);
                missPkUidSet.add(win1Uid);
                missPkUidSet.add(win2Uid);
            }
            activityCommonRedis.setCommonHashData(hashPkActivityId, String.valueOf(stageIndex), JSONObject.toJSONString(pkRankStrList));
            activityCommonRedis.addSetValues(getSetTeamPkKey(activityId, stageIndex), missPkUidSet);
            activityCommonRedis.setCommonHashNum(hashPkActivityId, stageOverKey, 1);
            cacheDataService.delSetCache(getSetTeamPkKey(activityId, stageIndex), 2);
        }
    }

    public void handleLastRoundData(String activityId) {
        int stageIndex = STAGE_START_TIME_LIST.size();
        distributionTotalRanking(activityId, stageIndex);
    }

    /**
     * 下发团队top1奖励
     */
    public void distributionTotalRanking(String activityId, int stageIndex) {
        try {
            Map<String, Integer> teamRankMap = activityCommonRedis.getCommonRankingMap(getZSetTeamRankKey(activityId, stageIndex), 1);
            int rank = 1;
            for (Map.Entry<String, Integer> entry : teamRankMap.entrySet()) {
                if (rank == 1) {
                    String teamId = entry.getKey();
                    List<String> teamMemberUidList = activityCommonRedis.getCommonListRecord(getListTeamMemberKey(activityId, teamId), 3);
                    for (String rankUid : teamMemberUidList) {
                        resourceKeyHandlerService.sendResourceData(rankUid, TEAM_FINAL_WINNER_TOP1, "Football Carnival-TeamTopreward", "Football Carnival-TeamTopreward");
                    }
                }
                rank += 1;
            }
        } catch (Exception e) {
            logger.error("distributionTotalRanking error: {}", e.getMessage(), e);
        }
    }

    /**
     * 分享配置信息给好友
     */
    public void footballCarnivalShare(ShareActivityDTO dto) {
        if (StringUtils.isEmpty(dto.getActivity_id()) || StringUtils.isEmpty(dto.getAid())) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        checkActivityTime(dto.getActivity_id());
        String uid = dto.getUid(); // 邀请者 ，需要加入的队伍
        String activityId = dto.getActivity_id();
        String aid = dto.getAid(); // 被邀请者
        if (uid.equals(aid)) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }


        String hashActivityId = getHashActivityId(activityId, uid);
        String teamId = activityCommonRedis.getCommonHashStrValue(hashActivityId, TEAM_ID_KEY);

        if (!ObjectUtils.isEmpty(teamId)) {
            String teamMemberKey = getListTeamMemberKey(activityId, teamId);
            List<String> teamMemberUidList = activityCommonRedis.getCommonListRecord(teamMemberKey, 3);
            if (teamMemberUidList.contains(aid)) {
                logger.info("FootballCarnivalShare teamMemberUidList.contains(aid) activityId:{} aid:{}", activityId, aid);
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "أنتم بالفعل زملاء في الفريق");
            }

            if (teamMemberUidList.size() >= 3) {
                logger.info("FootballCarnivalShare teamMemberUidList.size() >= 3 activityId:{}", activityId);
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "عضو فريق الصديق ممتلئ");
            }
        }

        String hashAidActivityId = getHashActivityId(activityId, aid);
        String teamAidId = activityCommonRedis.getCommonHashStrValue(hashAidActivityId, TEAM_ID_KEY);
        if (!ObjectUtils.isEmpty(teamAidId)) {
            logger.info("FootballCarnivalShare teamAidId is not empty activityId:{}", activityId);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "لقد انضم هذا الصديق الي فريق آخر");
        }

        String shareLimitKey = getShareLimitKey(activityId, uid, aid, DateHelper.ARABIAN.formatDateInDay());
        String shareLimit = activityCommonRedis.getCommonStrValue(shareLimitKey);
        if (!StringUtils.isEmpty(shareLimit)) {
            throw new CommonH5Exception(ActivityHttpCode.ALREADY_SHARE.getCode(), "لقد قمت بدعوته/ها اليوم , يرجي المحاولة غدا.");
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("activity_id", dto.getActivity_id());
        jsonObject.put("activity_name", dto.getActivity_name());
        jsonObject.put("activity_desc", dto.getActivity_desc());
        jsonObject.put("activity_icon", dto.getActivity_icon());
        jsonObject.put("activity_banner", dto.getActivity_banner());
        jsonObject.put("activity_url", dto.getActivity_url());
        sendActivityShareMsg(dto.getUid(), dto.getAid(), jsonObject);
        activityCommonRedis.setCommonStrScore(shareLimitKey, 1);

    }


    /**
     * 上报石头数量
     */
    private void doReportSpecialItemsEvent(String activityId, String uid, int action, int num, int source) {
        ActivitySpecialItemsChangeEvent event = new ActivitySpecialItemsChangeEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setChange_action(action);
        event.setActivity_special_items_id("0");
        event.setActivity_name("Football Carnival");
        event.setActive_id(activityId);
        event.setActivity_special_items_resource(source);
        event.setResource_desc("");
        event.setChange_nums(num);
        eventReport.track(new EventDTO(event));
    }

    /**
     * 上报抽奖记录
     */
    private void doDrawPrizeRecordEvent(String uid, int senceDetail, int amount, ResourceKeyConfigData.ResourceMeta resourceMeta,Integer successNum) {
        DrawPrizeRecordEvent event = new DrawPrizeRecordEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setSence("Football Carnival");
        event.setSence_detail(senceDetail);
        event.setTicket_type(0);
        event.setCost_ticket(amount);
        event.setDraw_nums(amount);
        if (successNum != null) {
            event.setDraw_success_nums(successNum);
        }else {
            event.setDraw_success_nums(amount);
        }
        event.setDraw_result(JSONObject.toJSONString(resourceMeta));
        eventReport.track(new EventDTO(event));
    }


    /**
     * 上报组队情况
     */
    private void doTeamRecordEvent(String activityId, String uid, String captainUid, String teamName) {
        TeamRecordEvent event = new TeamRecordEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setActivity_name("Football Carnival");
        event.setActive_id(activityId);
        event.setTeam_captain_uid(captainUid);
        event.setTeam_name(teamName);
        eventReport.track(new EventDTO(event));
    }


}

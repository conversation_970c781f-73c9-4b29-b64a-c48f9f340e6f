package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.*;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.SudGameInfo;
import com.quhong.data.TurntableGameInfo;
import com.quhong.data.vo.GameCarnivalVO;
import com.quhong.data.vo.OtherRankConfigVO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.data.vo.TaskConfigVO;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.enums.SudGameConstant;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.SudGameDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.SudGameData;
import com.quhong.mysql.dao.RoomBlacklistDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.redis.RoomPwdRedis;
import com.quhong.redis.SudGameRedis;
import com.quhong.redis.TurntableGameRedis;
import com.quhong.room.redis.RoomKickRedis;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class GameCarnivalService extends OtherActivityService implements TaskMsgHandler {


    private static final Logger logger = LoggerFactory.getLogger(GameCarnivalService.class);
    private static final String ACTIVITY_TITLE_EN = "Anniversary Game Carnival";
    public static String ACTIVITY_ID = "dsss";

    private static String ACTIVITY_URL = String.format("https://static.youstar.live/game_garnival2025/?activityId=%s", ACTIVITY_ID);
    private static final String ACTIVITY_BROADCAST_ICON = "";

    private static final Integer LUCKY_WHEEL = 100;  // 转盘游戏
    private static final Integer ALL_GAME_TYPE = -1; // 所有游戏

    private static final List<TaskConfigVO> DAILY_TASK_LIST = new ArrayList<>(); // 每日任务

    /**
     * 任意游戏赢得次数总计，榜单前10名奖励
     */
    public static final List<String> RANK_KEY_LIST = Arrays.asList(
            "GameCarnivalTop1", "GameCarnivalTop2", "GameCarnivalTop3"
            , "GameCarnivalTop4", "GameCarnivalTop5", "GameCarnivalTop6-10"
            , "GameCarnivalTop6-10", "GameCarnivalTop6-10", "GameCarnivalTop6-10", "GameCarnivalTop6-10");


    /**
     * 累计任务，任意游戏完成相应累积天数即可
     */
    public static final Map<Integer, String> TOTAL_RES_KEY_MAP = new HashMap<Integer, String>() {
        {
            put(2, "GameCarnival2days"); // 2天
            put(4, "GameCarnival4days"); // 4天
            put(6, "GameCarnival6days"); // 6天
        }
    };

    /**
     * 任意游戏赢得10钻,100钻,500钻奖励
     */
    // private static final List<Integer> WIN_DIAMOND_LIST = Arrays.asList(0, 10, 100, 500);
    // private static final List<String> RES_LEVEL_LIST = Arrays.asList("", "GameCarnival10Diamonds", "GameCarnival100Diamonds", "GameCarnival500Diamonds");

    /**
     * 每日任务
     */
    public static final Map<Integer, String> DAILY_RES_KEY_MAP = new HashMap<Integer, String>() {
        {
            put(0, "GameCarnival10Diamonds"); // 在任意游戏中共赢得10钻
            put(1, "GameCarnival100Diamonds"); // 在任意游戏中共赢得100钻
            put(2, "GameCarnival500Diamonds"); // 在任意游戏中共赢得500钻
            put(3, "GameCarnivalWin3Games"); // 在任意3种不同游戏中各赢1局
            put(4, "GameCarnivalWin5Carrom"); // 赢5局克罗姆游戏
        }
    };


    /**
     * 埋点事件
     */
    public static final Map<Integer, String> EVENT_TITLE_MAP = new HashMap<Integer, String>() {
        {
            put(0, "Game Carnival");
            put(102, "Game Carnival rank");

        }
    };

    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private static final List<String> TASK_ITEM_LIST = Arrays.asList(
            CommonMqTaskConstant.PLAY_WOISSPY_GAME, CommonMqTaskConstant.WIN_WOISSPY_GAME,
            CommonMqTaskConstant.PLAY_LUDO, CommonMqTaskConstant.WIN_LUDO,
            CommonMqTaskConstant.PLAY_CARROM_POOL, CommonMqTaskConstant.WIN_CARROM_POOL,
            CommonMqTaskConstant.PLAY_MONSTER_CRUSH, CommonMqTaskConstant.WIN_MONSTER_CRUSH,
            CommonMqTaskConstant.PLAY_JACKAROO, CommonMqTaskConstant.WIN_JACKAROO,
            CommonMqTaskConstant.PLAY_BALOOT, CommonMqTaskConstant.WIN_BALOOT,
            CommonMqTaskConstant.PLAY_DOMINO, CommonMqTaskConstant.WIN_DOMINO,
            CommonMqTaskConstant.PLAY_UMO, CommonMqTaskConstant.WIN_UMO,
            CommonMqTaskConstant.PLAY_WHEEL, CommonMqTaskConstant.WIN_LUCKY_WHEEL
    );

    private static final List<Integer> TOTAL_REWARD_LIST = Arrays.asList(
            2, 4, 6);

    private static final List<Integer> ALL_SUD_GAME_TYPE = Arrays.asList(
            SudGameConstant.WOISSPY_GAME, SudGameConstant.LUDO_GAME, SudGameConstant.CARROM_POOL_GAME,
            SudGameConstant.MONSTER_CRUSH_GAME, SudGameConstant.JACKAROO_GAME, SudGameConstant.BALOOT_GAME,
            SudGameConstant.DOMINO_GAME, SudGameConstant.UMO_GAME);

    static {
        DAILY_TASK_LIST.add(new TaskConfigVO
                (10, 0, "win_10_diamond", "", "", "", 0, "", "", "", DAILY_RES_KEY_MAP.get(0)));
        DAILY_TASK_LIST.add(new TaskConfigVO
                (100, 0, "win_100_diamond", "", "", "", 0, "", "", "", DAILY_RES_KEY_MAP.get(1)));
        DAILY_TASK_LIST.add(new TaskConfigVO
                (500, 0, "win_500_diamond", "", "", "", 0, "", "", "", DAILY_RES_KEY_MAP.get(2)));
        DAILY_TASK_LIST.add(new TaskConfigVO
                (3, 0, "win_3_games_in_different_games", "", "", "", 0, "", "", "", DAILY_RES_KEY_MAP.get(3)));
        DAILY_TASK_LIST.add(new TaskConfigVO
                (5, 0, "win_5_carrom_games", "", "", "", 0, "", "", "", DAILY_RES_KEY_MAP.get(4)));
    }

    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private CacheDataService cacheDataService;
    @Resource
    private EventReport eventReport;
    @Resource
    private SudGameDao sudGameDao;
    @Resource
    private SudGameRedis sudGameRedis;
    @Resource
    private RoomBlacklistDao roomBlacklistDao;
    @Resource
    private RoomKickRedis roomKickRedis;
    @Resource
    private TurntableGameRedis turntableGameRedis;
    @Resource
    private RoomPwdRedis roomPwdRedis;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            ACTIVITY_ID = "dsss";
            ACTIVITY_URL = String.format("https://test2.qmovies.tv/game_garnival2025/?activityId=%s", ACTIVITY_ID);

        }
    }

    /**
     * @param activityId
     * @param uid
     * @param gameType
     * @return
     * @see SudGameConstant
     */
    public GameCarnivalVO recommendRoomId(String activityId, String uid, int gameType) {
        if (!ALL_SUD_GAME_TYPE.contains(gameType) && gameType != LUCKY_WHEEL && gameType != ALL_GAME_TYPE) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        GameCarnivalVO vo = new GameCarnivalVO();
        if (gameType == LUCKY_WHEEL) {
            vo.setRecommendRoomId(matchingTurntableGameRoomId(uid));
            return vo;
        } else {
            // 校验玩家是否有未退出的游戏
            String inGameId = sudGameRedis.getPlayerData(uid);
            if (null != inGameId) {
                SudGameInfo sudGameInfo = sudGameRedis.getSudGameInfo(inGameId);
                if (gameType == ALL_GAME_TYPE && RoomUtils.isVoiceRoom(sudGameInfo.getRoomId()) && ALL_SUD_GAME_TYPE.contains(sudGameInfo.getGameType())) {
                    vo.setRecommendRoomId(sudGameInfo.getRoomId());
                    return vo;
                } else if (gameType == sudGameInfo.getGameType() && RoomUtils.isVoiceRoom(sudGameInfo.getRoomId())) {
                    vo.setRecommendRoomId(sudGameInfo.getRoomId());
                    return vo;
                }
                logger.info("already in game gameId:{} roomId:{} gameType:{}", inGameId, sudGameInfo.getRoomId(), sudGameInfo.getGameType());
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "أنت في اللعبة");
            }
            List<SudGameData> matchingByGameType = sudGameDao.findMatchingByGameType(gameType, SudGameConstant.VOICE_ROOM);
            Collections.shuffle(matchingByGameType);
            for (SudGameData sudGameData : matchingByGameType) {
                MongoRoomData roomData = mongoRoomDao.findData(sudGameData.getRoomId());
                if (null != roomData && StringUtils.isEmpty(roomData.getPwd())
                        && !roomBlacklistDao.isBlock(sudGameData.getRoomId(), uid)
                        && !roomKickRedis.isKick(sudGameData.getRoomId(), uid)) {
                    vo.setRecommendRoomId(sudGameData.getRoomId());
                    break;
                }
            }
            if (StringUtils.isEmpty(vo.getRecommendRoomId())) {
                vo.setRecommendRoomId(RoomUtils.formatRoomId(uid));
            }
            return vo;
        }
    }

    public String matchingTurntableGameRoomId(String uid) {
        List<TurntableGameInfo> turntableGameInfoList = turntableGameRedis.getAllTurntableGame();
        for (TurntableGameInfo gameInfo : turntableGameInfoList) {
            String roomId = gameInfo.getRoomId();
            String hostUid = RoomUtils.getRoomHostId(roomId);
            if (actorDao.getActorDataFromCache(hostUid).getRobot() == 1) {
                continue;
            }
            if (gameInfo.getStatus() != 1 || roomBlacklistDao.isBlock(roomId, uid) || roomKickRedis.isKick(roomId, uid)) {
                continue;
            }
            if (roomPwdRedis.hasPwdFromCache(roomId)) {
                continue;
            }
            List<TurntableGameInfo.Actor> playerList = gameInfo.getPlayers();
            if (playerList == null || playerList.isEmpty()) {
                continue;
            }
            return roomId;
        }
        return RoomUtils.formatRoomId(uid);
    }

    public GameCarnivalVO gameCarnivalInfo(String activityId, String uid) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(activityId);
        if (activityData == null) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        String totalInfoKey = getHashTotalKey(activityId);
        String now = getDayByBase(activityId, uid, true, activityData.getAcNameEn().startsWith("test"));
        String detailUserKey = getUserHashDetailKey(activityId, now);
        GameCarnivalVO.TotalInfo totalInfo = cacheDataService.
                getGameCarnivalVOTotalInfo(totalInfoKey, uid);
        GameCarnivalVO.DetailInfo detailInfo = cacheDataService.
                getGameCarnivalVODetailInfo(detailUserKey, now, uid);

        GameCarnivalVO vo = new GameCarnivalVO();
        GameCarnivalVO.TotalTaskVO totalTaskVO = new GameCarnivalVO.TotalTaskVO();
        List<TaskConfigVO> dailyTaskList = new ArrayList<>();

        for (int i = 0; i < DAILY_TASK_LIST.size(); i++) {
            TaskConfigVO item = DAILY_TASK_LIST.get(i);
            TaskConfigVO itemVO = new TaskConfigVO();
            itemVO.setTotalProcess(item.getTotalProcess());
            itemVO.setTaskKey(item.getTaskKey());
            fillTaskConfigVO(itemVO, detailInfo, i);
            dailyTaskList.add(itemVO);
        }
        totalTaskVO.setTotalDayCount(totalInfo.getDaySet().size());

        vo.setTotalTaskVO(totalTaskVO);
        vo.setDailyTaskList(dailyTaskList);
        fillRankList(uid, activityId, vo);
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());
        return vo;
    }

    private void fillTaskConfigVO(TaskConfigVO itemVO, GameCarnivalVO.DetailInfo detailInfo, int pos) {
        int maxLimit = itemVO.getTotalProcess();
        int nowNum = 0;
        switch (pos) {
            case 0:
                nowNum = detailInfo.getWinDiamondNum1();
                break;
            case 1:
                nowNum = detailInfo.getWinDiamondNum2();
                break;
            case 2:
                nowNum = detailInfo.getWinDiamondNum3();
                break;
            case 3:
                nowNum = detailInfo.getWinGameType().size() >= 3 ? 3 : detailInfo.getWinGameType().size();
                break;
            case 4:
                nowNum = detailInfo.getWinCarromCount();
                break;
        }
        itemVO.setCurrentProcess(nowNum);
        itemVO.setStatus(nowNum >= maxLimit ? 1 : 0);
    }

    private void fillRankList(String uid, String activityId, GameCarnivalVO vo) {
        // 克莱姆游戏赢的次数榜单
        List<OtherRankingListVO> rankList = new ArrayList<>();
        OtherRankingListVO myRankVO = new OtherRankingListVO();
        String typeKey = getZetTypeKey(activityId);
        Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(typeKey, 10);
        int rank = 1;
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            String itemUid = entry.getKey();
            Integer winCount = entry.getValue();
            ActorData actorData = actorDao.getActorDataFromCache(itemUid);
            OtherRankingListVO rankVO = new OtherRankingListVO();
            rankVO.setUid(itemUid);
            rankVO.setName(actorData.getName());
            rankVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            rankVO.setScore(winCount);
            rankVO.setVipLevel(vipInfoDao.getIntVipLevel(itemUid));
            rankVO.setBadgeList(badgeDao.getBadgeList(itemUid));
            rankVO.setRank(rank);
            rankList.add(rankVO);
            if (itemUid.equals(uid)) {
                BeanUtils.copyProperties(rankVO, myRankVO);
            }
            rank++;
        }
        if (StringUtils.isEmpty(myRankVO.getUid())) {
            ActorData myActorData = actorDao.getActorDataFromCache(uid);
            myRankVO.setUid(uid);
            myRankVO.setName(myActorData.getName());
            myRankVO.setHead(ImageUrlGenerator.generateRoomUserUrl(myActorData.getHead()));
            myRankVO.setScore(activityCommonRedis.getCommonZSetRankingScore(typeKey, uid));
            myRankVO.setVipLevel(vipInfoDao.getIntVipLevel(uid));
            myRankVO.setRank(activityCommonRedis.getCommonZSetRank(typeKey, uid));
        }
        myRankVO.setBadgeList(badgeDao.getBadgeList(uid));
        vo.setRankList(rankList);
        vo.setMyRankVO(myRankVO);
    }


    @Override
    public void taskMsgProcess(CommonMqTopicData data) {
        String uid = data.getUid();
        String aid = data.getAid();
        String roomId = data.getRoomId();
        String item = data.getItem();
        if (!TASK_ITEM_LIST.contains(item)) {
            return;
        }
        if (getOtherRankingActivityNull(ACTIVITY_ID) == null) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }
        syncAddHandle(uid, data);
    }


    private boolean checkAc(String uid, CommonMqTopicData mqData) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {
            boolean isWhiteTest = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
            if (!isWhiteTest) {
                // 灰度测试,只统计测试用户的
                return false;
            }
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            // 临界时间处理
            return false;
        }
        return true;
    }

    private void syncAddHandle(String uid, CommonMqTopicData mqData) {
        if (!checkAc(uid, mqData)) {
            return;
        }
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        String now = getDayByBase(ACTIVITY_ID, uid, true, activityData.getAcNameEn().startsWith("test"));
        String totalInfoKey = getHashTotalKey(null);
        String detailUserKey = getUserHashDetailKey(null, now);
        String typeKey = getZetTypeKey(null);
        String item = mqData.getItem();
        synchronized (stringPool.intern(getLocalEventUserKey(uid))) {
            GameCarnivalVO.DetailInfo detailInfo = cacheDataService.getGameCarnivalVODetailInfo(detailUserKey, now, uid);
            int addScore = 0; // 1 更新detailInfo
            int maxLimit = 0;
            int nowNum = 0;
            TaskConfigVO taskConfig;
            String itemKey = "";
            if (CommonMqTaskConstant.PLAY_LUDO.equals(item)) {
                // 玩游戏
                GameCarnivalVO.TotalInfo totalInfo = cacheDataService.getGameCarnivalVOTotalInfo(totalInfoKey, uid);
                if (RoomUtils.isHomeowner(mqData.getUid(), mqData.getRoomId())) {
                    // 在自己房间玩游戏
                    taskConfig = DAILY_TASK_LIST.get(2);
                    maxLimit = taskConfig.getTotalProcess();
                    nowNum = detailInfo.getPlayInMyRoomCount();
                    if (nowNum < maxLimit) {
                        detailInfo.setPlayInMyRoomCount(nowNum + 1);
                        if (detailInfo.getPlayInMyRoomCount() == maxLimit) {
                            handleRes(uid, DAILY_RES_KEY_MAP.get(2), 0);
                        }
                        addScore = 1;
                    }
                }

                // 玩ludo游戏5局
                taskConfig = DAILY_TASK_LIST.get(3);
                maxLimit = taskConfig.getTotalProcess();
                nowNum = detailInfo.getPlayGameCount();
                if (nowNum < maxLimit) {
                    detailInfo.setPlayGameCount(nowNum + 1);
                    if (detailInfo.getPlayGameCount() == maxLimit) {
                        handleRes(uid, DAILY_RES_KEY_MAP.get(3), 0);
                    }
                    addScore = 1;
                }

                Set<String> daySet = totalInfo.getDaySet();
                if (!daySet.contains(now)) {
                    daySet.add(now);
                    int totalDayCount = daySet.size();
                    if (TOTAL_REWARD_LIST.contains(totalDayCount)) {
                        handleRes(uid, TOTAL_RES_KEY_MAP.getOrDefault(totalDayCount, ""), 0);
                    }
                    activityCommonRedis.setCommonHashData(totalInfoKey, uid, JSONObject.toJSONString(totalInfo));
                    logger.info("success add sign day uid:{} totalDayCount:{} daySet:{}", uid, totalDayCount, daySet);
                }

            } else if (CommonMqTaskConstant.WIN_LUDO.equals(item)) {
                // 赢游戏

                uid = mqData.getUid();
//                String jsonStr = mqData.getJsonData(); // 1 coin 2 diamond
//                JSONObject dataMap = JSONObject.parseObject(jsonStr);
                CommonMqTopicData.WinGameInfo winGameInfo = JSONObject.parseObject(mqData.getJsonData(), CommonMqTopicData.WinGameInfo.class);
                int awardValue = winGameInfo.getAwardValue();
                int currencyType = winGameInfo.getCurrencyType();
                if (1 == currencyType) {
                    // 金币
                    taskConfig = DAILY_TASK_LIST.get(0);
                    maxLimit = taskConfig.getTotalProcess();
                    nowNum = detailInfo.getWinGoldCount();
                    if (nowNum < maxLimit) {
                        detailInfo.setWinGoldCount(nowNum + 1);
                        if (detailInfo.getWinGoldCount() == maxLimit) {
                            handleRes(uid, DAILY_RES_KEY_MAP.get(0), 0);
                        }
                        addScore = 1;
                    }

                } else if (2 == currencyType) {
                    // 钻石
                    taskConfig = DAILY_TASK_LIST.get(1);
                    maxLimit = taskConfig.getTotalProcess();
                    nowNum = detailInfo.getWinDiamondCount();
                    if (nowNum < maxLimit) {
                        detailInfo.setWinDiamondCount(nowNum + 1);
                        if (detailInfo.getWinDiamondCount() == maxLimit) {
                            handleRes(uid, DAILY_RES_KEY_MAP.get(1), 0);
                        }
                        addScore = 1;
                    }

                    taskConfig = DAILY_TASK_LIST.get(4);
                    maxLimit = taskConfig.getTotalProcess();
                    nowNum = detailInfo.getWinDiamondNum2();
                    if (nowNum < maxLimit && awardValue > 0) {
                        detailInfo.setWinDiamondNum2(Math.min(nowNum + awardValue, maxLimit));
                        if (detailInfo.getWinDiamondNum2() == maxLimit) {
                            handleRes(uid, DAILY_RES_KEY_MAP.get(4), 0);
                        }
                        addScore = 1;
                    }

                }
                activityCommonRedis.incrCommonZSetRankingScoreSimple(typeKey, uid, 1);
            } else if (mqData.getItem().equals("game_event_test")) {
                // 加积分
                addScore = mqData.getValue();
            }

            if (addScore > 0) {
                String jsonStr = JSONObject.toJSONString(detailInfo);
                activityCommonRedis.setCommonHashData(detailUserKey, uid, jsonStr);
                logger.info("success add uid:{} detailInfo:{} mqData:{}",
                        uid, jsonStr, JSON.toJSONString(mqData));
            }
        }
    }


    private void sendOfficialMsg(List<String> titleList, List<String> bodyList, String item) {
        ActorData actorData = actorDao.getActorDataFromCache(item);
        int slang = actorData.getSlang();
        String title = slang == SLangType.ARABIC ? titleList.get(1) : titleList.get(0);
        String body = slang == SLangType.ARABIC ? bodyList.get(1) : bodyList.get(0);
        commonOfficialMsg(item, "", 0, 0, "",
                title, body, ACTIVITY_URL);
    }


    // 下发榜单奖励
    public void distributionRanking() {
        try {
            int length = 10;
            Map<String, Integer> totalRankingMap = activityCommonRedis.getCommonRankingMap(getZetTypeKey(null), length);
            int rank = 1;
            for (Map.Entry<String, Integer> entry : totalRankingMap.entrySet()) {
                if (rank > length) {
                    continue;
                }
                String aid = entry.getKey();
                String resKey = RANK_KEY_LIST.get(rank - 1);
                handleRes(aid, resKey, 102);
                rank += 1;
            }
        } catch (Exception e) {
            logger.error("distributionRanking error: {}", e.getMessage(), e);
        }
    }

    private void handleRes(String aid, String resKey, int atype) {
        String eventTitle = EVENT_TITLE_MAP.getOrDefault(atype, "");
        resourceKeyHandlerService.sendResourceData(aid, resKey,
                eventTitle, eventTitle, eventTitle, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
    }


    private void doJoinReportEvent(String uid) {
        ActivityApplicationEvent event = new ActivityApplicationEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setScene(ACTIVITY_TITLE_EN);
        eventReport.track(new EventDTO(event));
    }

    private void doDoneQueenEvent(String uid, int type) {
        ActivityParticipationEvent event = new ActivityParticipationEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setActivity_name(ACTIVITY_TITLE_EN);
        event.setActive_id(ACTIVITY_ID);
        event.setActivity_stage(type);
        eventReport.track(new EventDTO(event));
    }

    private void doReportDetailEvent(String uid, int type, int changed, String changedDesc) {
        ScoreRecordEvent event = new ScoreRecordEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setScore_changed(changed);
        event.setScore_changed_sence(21);
        event.setScore_changed_detail(type);
        event.setScore_changed_desc(changed > 0 ? changedDesc : "daily_reward");
        eventReport.track(new EventDTO(event));
    }


    private String getLocalEventUserKey(String uid) {
        return "lock:game_carnival:user:" + uid;
    }


    private String getUserHashDetailKey(String activityId, String dayStr) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return aId + ":game_carnival:user:detail:" + dayStr;
    }

    private String getHashTotalKey(String activityId) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return aId + ":game_carnival:total";
    }

    private String getZetTypeKey(String activityId) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return aId + ":game_carnival";
    }


}

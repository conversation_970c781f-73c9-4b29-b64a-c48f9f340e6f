package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.*;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.SudGameInfo;
import com.quhong.data.vo.CarromMasterVO;
import com.quhong.data.vo.OtherRankConfigVO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.data.vo.TaskConfigVO;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.SLangType;
import com.quhong.enums.SudGameConstant;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.SudGameDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.SudGameData;
import com.quhong.mysql.dao.RoomBlacklistDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.redis.SudGameRedis;
import com.quhong.room.redis.RoomKickRedis;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;


@Service
public class MonsterCrushMasterService extends OtherActivityService implements TaskMsgHandler {


    private static final Logger logger = LoggerFactory.getLogger(MonsterCrushMasterService.class);
    private static final String ACTIVITY_TITLE_EN = "Monster Crush Master";
    public static final String ACTIVITY_ID = "67f722e827a11d4eb9028007";

    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/MonsterCrushMaster/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/MonsterCrushMaster/?activityId=%s", ACTIVITY_ID);
    private static final String ACTIVITY_BROADCAST_ICON = "";


    private static final List<TaskConfigVO> DAILY_TASK_LIST = new ArrayList<>(); // 每日任务

    /**
     * 榜单前10名奖励
     */
    public static final List<String> RANK_KEY_LIST = Arrays.asList(
            "MonsterCrushTop1", "MonsterCrushTop2", "MonsterCrushTop3"
            , "MonsterCrushTop4", "MonsterCrushTop5", "MonsterCrushTop6-10"
            , "MonsterCrushTop6-10", "MonsterCrushTop6-10", "MonsterCrushTop6-10", "MonsterCrushTop6-10");

    /**
     * 累计任务
     */
    public static final Map<Integer, String> TOTAL_RES_KEY_MAP = new HashMap<Integer, String>() {
        {
            put(2, "CarromMaster2days"); // 2天
            put(4, "CarromMaster4days"); // 4天
            put(6, "CarromMaster6days"); // 6天
        }
    };

    /**
     * 每日任务
     */
    public static final Map<Integer, String> DAILY_RES_KEY_MAP = new HashMap<Integer, String>() {
        {
            put(0, "CarromMasterCoin"); //在MonsterCrush游戏中赢3局金币
            put(1, "CarromMasterDiamond"); // 在MonsterCrush游戏中赢3局钻石
            put(2, "MonsterCrushMasterRoom"); // 在自己房间内玩2局MonsterCrush游戏
            put(3, "GameMasterBubbleFrame"); // 玩7局MonsterCrush游戏
            put(4, "GameMasterGift"); // 玩MonsterCrush游戏赢28钻
        }
    };


    /**
     * 埋点事件
     */
    public static final Map<Integer, String> EVENT_TITLE_MAP = new HashMap<Integer, String>() {
        {
            put(0, "MonsterCrush Master");
            put(102, "MonsterCrush Master rank");

        }
    };

    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private static final List<String> TASK_ITEM_LIST = Arrays.asList(
            CommonMqTaskConstant.PLAY_MONSTER_CRUSH, CommonMqTaskConstant.WIN_MONSTER_CRUSH);

    private static final List<Integer> TOTAL_REWARD_LIST = Arrays.asList(
            2, 4, 6);

    static {
        DAILY_TASK_LIST.add(new TaskConfigVO
                (3, 0, "win_3_gold_round_monster", "", "", "", 0, "", "", "", DAILY_RES_KEY_MAP.get(0)));
        DAILY_TASK_LIST.add(new TaskConfigVO
                (3, 0, "win_3_diamond_round_monster", "", "", "", 0, "", "", "", DAILY_RES_KEY_MAP.get(1)));
        DAILY_TASK_LIST.add(new TaskConfigVO
                (2, 0, "play_2_round_monster_myRoom", "", "", "", 0, "", "", "", DAILY_RES_KEY_MAP.get(2)));
        DAILY_TASK_LIST.add(new TaskConfigVO
                (7, 0, "play_7_round_monster", "", "", "", 0, "", "", "", DAILY_RES_KEY_MAP.get(3)));
        DAILY_TASK_LIST.add(new TaskConfigVO
                (28, 0, "win_28_diamond_num_monster", "", "", "", 0, "", "", "", DAILY_RES_KEY_MAP.get(4)));
    }

    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private CacheDataService cacheDataService;
    @Resource
    private EventReport eventReport;
    @Resource
    private SudGameDao sudGameDao;
    @Resource
    private SudGameRedis sudGameRedis;
    @Resource
    private RoomBlacklistDao roomBlacklistDao;
    @Resource
    private RoomKickRedis roomKickRedis;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
        }
    }

    public CarromMasterVO monsterMasterRoomId(String activityId, String uid) {
        CarromMasterVO vo = new CarromMasterVO();
        // 校验玩家是否有未退出的游戏
        String inGameId = sudGameRedis.getPlayerData(uid);
        if (null != inGameId) {
            SudGameInfo sudGameInfo = sudGameRedis.getSudGameInfo(inGameId);
            if (SudGameConstant.MONSTER_CRUSH_GAME == sudGameInfo.getGameType() && RoomUtils.isVoiceRoom(sudGameInfo.getRoomId())) {
                vo.setRecommendRoomId(sudGameInfo.getRoomId());
                return vo;
            }
            logger.info("already in game gameId:{} roomId:{} gameType:{}", inGameId, sudGameInfo.getRoomId(), sudGameInfo.getGameType());
            throw new CommonH5Exception(ActivityHttpCode.MONSTER_MASTER_PLAYER_IN_GAME);
        }
        List<SudGameData> matchingByGameType = sudGameDao.findMatchingByGameType(SudGameConstant.MONSTER_CRUSH_GAME, SudGameConstant.VOICE_ROOM);
        Collections.shuffle(matchingByGameType);
        for (SudGameData sudGameData : matchingByGameType) {
            MongoRoomData roomData = mongoRoomDao.findData(sudGameData.getRoomId());
            if (null != roomData && StringUtils.isEmpty(roomData.getPwd())
                    && !roomBlacklistDao.isBlock(sudGameData.getRoomId(), uid)
                    && !roomKickRedis.isKick(sudGameData.getRoomId(), uid)) {
                vo.setRecommendRoomId(sudGameData.getRoomId());
                break;
            }
        }
        if (StringUtils.isEmpty(vo.getRecommendRoomId())) {
            vo.setRecommendRoomId(RoomUtils.formatRoomId(uid));
        }
        return vo;
    }

    public CarromMasterVO monsterMasterInfo(String activityId, String uid) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(activityId);
        if (activityData == null) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        String totalInfoKey = getHashTotalKey(activityId);
        String now = getDay(uid, true, activityData.getAcNameEn().startsWith("test"));
        String detailUserKey = getUserHashDetailKey(activityId, now);
        CarromMasterVO.TotalInfo totalInfo = cacheDataService.
                getCarromMasterVOTotalInfo(totalInfoKey, uid);
        CarromMasterVO.DetailInfo detailInfo = cacheDataService.
                getCarromMasterVODetailInfo(detailUserKey, now, uid);

        CarromMasterVO vo = new CarromMasterVO();
        CarromMasterVO.TotalTaskVO totalTaskVO = new CarromMasterVO.TotalTaskVO();
        List<TaskConfigVO> dailyTaskList = new ArrayList<>();

        for (int i = 0; i < DAILY_TASK_LIST.size(); i++) {
            TaskConfigVO item = DAILY_TASK_LIST.get(i);
            TaskConfigVO itemVO = new TaskConfigVO();
            itemVO.setTotalProcess(item.getTotalProcess());
            itemVO.setTaskKey(item.getTaskKey());
            fillTaskConfigVO(itemVO, detailInfo, i);
            dailyTaskList.add(itemVO);
        }
        totalTaskVO.setTotalDayCount(totalInfo.getDaySet().size());

        vo.setTotalTaskVO(totalTaskVO);
        vo.setDailyTaskList(dailyTaskList);
        fillRankList(uid, activityId, vo);
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());
        return vo;
    }

    private void fillTaskConfigVO(TaskConfigVO itemVO, CarromMasterVO.DetailInfo detailInfo, int pos) {
        int maxLimit = itemVO.getTotalProcess();
        int nowNum = 0;
        switch (pos) {
            case 0:
                nowNum = detailInfo.getWinGoldCount();
                break;
            case 1:
                nowNum = detailInfo.getWinDiamondCount();
                break;
            case 2:
                nowNum = detailInfo.getPlayInMyRoomCount();
                break;
            case 3:
                nowNum = detailInfo.getPlayGameCount();
                break;
            case 4:
                nowNum = detailInfo.getWinDiamondNum2();
                break;
        }
        itemVO.setCurrentProcess(nowNum);
        itemVO.setStatus(nowNum >= maxLimit ? 1 : 0);
    }

    private void fillRankList(String uid, String activityId, CarromMasterVO vo) {
        // 游戏赢的次数榜单
        List<OtherRankingListVO> rankList = new ArrayList<>();
        OtherRankingListVO myRankVO = new OtherRankingListVO();
        String typeKey = getZetTypeKey(activityId);
        Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(typeKey, 10);
        int rank = 1;
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            String itemUid = entry.getKey();
            Integer winCount = entry.getValue();
            ActorData actorData = actorDao.getActorDataFromCache(itemUid);
            OtherRankingListVO rankVO = new OtherRankingListVO();
            rankVO.setUid(itemUid);
            rankVO.setName(actorData.getName());
            rankVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            rankVO.setScore(winCount);
            rankVO.setVipLevel(vipInfoDao.getIntVipLevel(itemUid));
            rankVO.setBadgeList(badgeDao.getBadgeList(itemUid));
            rankVO.setRank(rank);
            rankList.add(rankVO);
            if (itemUid.equals(uid)) {
                BeanUtils.copyProperties(rankVO, myRankVO);
            }
            rank++;
        }
        if (StringUtils.isEmpty(myRankVO.getUid())) {
            ActorData myActorData = actorDao.getActorDataFromCache(uid);
            myRankVO.setUid(uid);
            myRankVO.setName(myActorData.getName());
            myRankVO.setHead(ImageUrlGenerator.generateRoomUserUrl(myActorData.getHead()));
            myRankVO.setScore(activityCommonRedis.getCommonZSetRankingScore(typeKey, uid));
            myRankVO.setVipLevel(vipInfoDao.getIntVipLevel(uid));
            myRankVO.setRank(activityCommonRedis.getCommonZSetRank(typeKey, uid));
        }
        myRankVO.setBadgeList(badgeDao.getBadgeList(uid));
        vo.setRankList(rankList);
        vo.setMyRankVO(myRankVO);
    }


    public OtherRankConfigVO testUidDay(int cmd, String uid, int addDays, int addValue) {
        if (ServerConfig.isProduct()) {
            throw new CommonH5Exception(ActivityHttpCode.AUTH_ERROR);
        }
        OtherRankConfigVO otherRankConfigVO = new OtherRankConfigVO();
        if (cmd == 1) {
            // 设置偏移天数
            activityCommonRedis.setCommonHashData(getHashTestDayKey(null), uid, String.valueOf(addDays));
            otherRankConfigVO.setScore(addDays);
        } else if (cmd == 2) {
            // 查询偏移天数
            int add = activityCommonRedis.getCommonHashValue(getHashTestDayKey(null), uid);
            otherRankConfigVO.setScore(add);
        } else if (cmd == 3) {
            //  执行当天的定时任务
        } else if (cmd == 4) {
            CommonMqTopicData mqData = new CommonMqTopicData();
            mqData.setRoomId(RoomUtils.formatRoomId(uid));
            mqData.setUid(uid);
            mqData.setValue(addValue);
            mqData.setItem("game_event_test");
            syncAddHandle(uid, mqData);
        }
        return otherRankConfigVO;
    }

    @Override
    public void taskMsgProcess(CommonMqTopicData data) {
        String uid = data.getUid();
        String aid = data.getAid();
        String roomId = data.getRoomId();
        String item = data.getItem();
        if (!TASK_ITEM_LIST.contains(item)) {
            return;
        }
        if (getOtherRankingActivityNull(ACTIVITY_ID) == null) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }
        syncAddHandle(uid, data);
    }


    private boolean checkAc(String uid, CommonMqTopicData mqData) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {
            boolean isWhiteTest = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
            if (!isWhiteTest) {
                // 灰度测试,只统计测试用户的
                return false;
            }
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            // 临界时间处理
            return false;
        }
        return true;
    }

    private void syncAddHandle(String uid, CommonMqTopicData mqData) {
        if (!checkAc(uid, mqData)) {
            return;
        }
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        String now = getDay(uid, true, activityData.getAcNameEn().startsWith("test"));
        String totalInfoKey = getHashTotalKey(null);
        String detailUserKey = getUserHashDetailKey(null, now);
        String typeKey = getZetTypeKey(null);
        String item = mqData.getItem();
        synchronized (stringPool.intern(getLocalEventUserKey(uid))) {
            CarromMasterVO.DetailInfo detailInfo = cacheDataService.getCarromMasterVODetailInfo(detailUserKey, now, uid);
            int addScore = 0; // 1 更新detailInfo
            int maxLimit = 0;
            int nowNum = 0;
            TaskConfigVO taskConfig;
            String itemKey = "";
            if (CommonMqTaskConstant.PLAY_MONSTER_CRUSH.equals(item)) {
                // 玩游戏
                CarromMasterVO.TotalInfo totalInfo = cacheDataService.getCarromMasterVOTotalInfo(totalInfoKey, uid);
                if (RoomUtils.isHomeowner(mqData.getUid(), mqData.getRoomId())) {
                    // 在自己房间玩游戏
                    taskConfig = DAILY_TASK_LIST.get(2);
                    maxLimit = taskConfig.getTotalProcess();
                    nowNum = detailInfo.getPlayInMyRoomCount();
                    if (nowNum < maxLimit) {
                        detailInfo.setPlayInMyRoomCount(nowNum + 1);
                        if (detailInfo.getPlayInMyRoomCount() == maxLimit) {
                            handleRes(uid, DAILY_RES_KEY_MAP.get(2), 0);
                        }
                        addScore = 1;
                    }
                }

                // 玩Monster游戏7局
                taskConfig = DAILY_TASK_LIST.get(3);
                maxLimit = taskConfig.getTotalProcess();
                nowNum = detailInfo.getPlayGameCount();
                if (nowNum < maxLimit) {
                    detailInfo.setPlayGameCount(nowNum + 1);
                    if (detailInfo.getPlayGameCount() == maxLimit) {
                        handleRes(uid, DAILY_RES_KEY_MAP.get(3), 0);
                    }
                    addScore = 1;
                }

                Set<String> daySet = totalInfo.getDaySet();
                if (!daySet.contains(now)) {
                    daySet.add(now);
                    int totalDayCount = daySet.size();
                    if (TOTAL_REWARD_LIST.contains(totalDayCount)) {
                        handleRes(uid, TOTAL_RES_KEY_MAP.getOrDefault(totalDayCount, ""), 0);
                    }
                    activityCommonRedis.setCommonHashData(totalInfoKey, uid, JSONObject.toJSONString(totalInfo));
                    logger.info("success add sign day uid:{} totalDayCount:{} daySet:{}", uid, totalDayCount, daySet);
                }

            } else if (CommonMqTaskConstant.WIN_MONSTER_CRUSH.equals(item)) {
                // 赢游戏

                uid = mqData.getUid();
//                String jsonStr = mqData.getJsonData(); // 1 coin 2 diamond
//                JSONObject dataMap = JSONObject.parseObject(jsonStr);
                CommonMqTopicData.WinGameInfo winGameInfo = JSONObject.parseObject(mqData.getJsonData(), CommonMqTopicData.WinGameInfo.class);
                int awardValue = winGameInfo.getAwardValue();
                int currencyType = winGameInfo.getCurrencyType();
                if (1 == currencyType) {
                    // 金币
                    taskConfig = DAILY_TASK_LIST.get(0);
                    maxLimit = taskConfig.getTotalProcess();
                    nowNum = detailInfo.getWinGoldCount();
                    if (nowNum < maxLimit) {
                        detailInfo.setWinGoldCount(nowNum + 1);
                        if (detailInfo.getWinGoldCount() == maxLimit) {
                            handleRes(uid, DAILY_RES_KEY_MAP.get(0), 0);
                        }
                        addScore = 1;
                    }

                } else if (2 == currencyType) {
                    // 钻石
                    taskConfig = DAILY_TASK_LIST.get(1);
                    maxLimit = taskConfig.getTotalProcess();
                    nowNum = detailInfo.getWinDiamondCount();
                    if (nowNum < maxLimit) {
                        detailInfo.setWinDiamondCount(nowNum + 1);
                        if (detailInfo.getWinDiamondCount() == maxLimit) {
                            handleRes(uid, DAILY_RES_KEY_MAP.get(1), 0);
                        }
                        addScore = 1;
                    }

                    taskConfig = DAILY_TASK_LIST.get(4);
                    maxLimit = taskConfig.getTotalProcess();
                    nowNum = detailInfo.getWinDiamondNum2();
                    if (nowNum < maxLimit && awardValue > 0) {
                        detailInfo.setWinDiamondNum2(Math.min(nowNum + awardValue, maxLimit));
                        if (detailInfo.getWinDiamondNum2() == maxLimit) {
                            handleRes(uid, DAILY_RES_KEY_MAP.get(4), 0);
                        }
                        addScore = 1;
                    }

                }
                activityCommonRedis.incrCommonZSetRankingScoreSimple(typeKey, uid, 1);
            } else if (mqData.getItem().equals("game_event_test")) {
                //加积分
                addScore = mqData.getValue();
            }

            if (addScore > 0) {
                String jsonStr = JSONObject.toJSONString(detailInfo);
                activityCommonRedis.setCommonHashData(detailUserKey, uid, jsonStr);
                logger.info("success add uid:{} detailInfo:{} mqData:{}",
                        uid, jsonStr, JSON.toJSONString(mqData));
            }
        }
    }


    private void sendOfficialMsg(List<String> titleList, List<String> bodyList, String item) {
        ActorData actorData = actorDao.getActorDataFromCache(item);
        int slang = actorData.getSlang();
        String title = slang == SLangType.ARABIC ? titleList.get(1) : titleList.get(0);
        String body = slang == SLangType.ARABIC ? bodyList.get(1) : bodyList.get(0);
        commonOfficialMsg(item, "", 0, 0, "",
                title, body, ACTIVITY_URL);
    }


    // 下发榜单奖励
    public void distributionRanking() {
        try {
            int length = 10;
            Map<String, Integer> totalRankingMap = activityCommonRedis.getCommonRankingMap(getZetTypeKey(null), length);
            int rank = 1;
            for (Map.Entry<String, Integer> entry : totalRankingMap.entrySet()) {
                if (rank > length) {
                    continue;
                }
                String aid = entry.getKey();
                String resKey = RANK_KEY_LIST.get(rank - 1);
                handleRes(aid, resKey, 102);
                rank += 1;
            }
        } catch (Exception e) {
            logger.error("distributionRanking error: {}", e.getMessage(), e);
        }
    }

    private void handleRes(String aid, String resKey, int atype) {
        String eventTitle = EVENT_TITLE_MAP.getOrDefault(atype, "");
        resourceKeyHandlerService.sendResourceData(aid, resKey,
                eventTitle, eventTitle, eventTitle, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
    }


    private void doJoinReportEvent(String uid) {
        ActivityApplicationEvent event = new ActivityApplicationEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setScene(ACTIVITY_TITLE_EN);
        eventReport.track(new EventDTO(event));
    }

    private void doDoneQueenEvent(String uid, int type) {
        ActivityParticipationEvent event = new ActivityParticipationEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setActivity_name(ACTIVITY_TITLE_EN);
        event.setActive_id(ACTIVITY_ID);
        event.setActivity_stage(type);
        eventReport.track(new EventDTO(event));
    }

    private void doReportDetailEvent(String uid, int type, int changed, String changedDesc) {
        ScoreRecordEvent event = new ScoreRecordEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setScore_changed(changed);
        event.setScore_changed_sence(21);
        event.setScore_changed_detail(type);
        event.setScore_changed_desc(changed > 0 ? changedDesc : "daily_reward");
        eventReport.track(new EventDTO(event));
    }

    private String getDay(String uid) {
        return getDay(uid, true, false);
    }

    private String getDay(String uid, boolean baseIsToday, boolean isGrayTest) {
        if (ServerConfig.isNotProduct() || isGrayTest) {
            return getTestDays(uid, baseIsToday);
        }
        return baseIsToday ? DateHelper.ARABIAN.formatDateInDay()
                : DateHelper.ARABIAN.getYesterdayStr(new Date());
    }


    private String getTestDays(String uid, boolean baseIsToday) {
        int addDays = activityCommonRedis.getCommonHashValue(getHashTestDayKey(null), uid);
        logger.info("test add uid:{} days:{}", uid, addDays);
        addDays = baseIsToday ? addDays : addDays - 1;
        if (addDays == 0) {
            return DateHelper.ARABIAN.formatDateInDay();
        }
        return todayMinusDays(addDays);
    }

    /**
     * @param days
     * @return
     */
    private String todayMinusDays(int days) {
        days = -days; // -1为明天  1为昨天
        LocalDate nowDate = DateSupport.ARABIAN.getToday();
        LocalDate oldDate = nowDate.minusDays(days);
        // 获取字符格式 yyyy-MM-dd
        return DateSupport.format(oldDate);
    }


    private String getLocalEventUserKey(String uid) {
        return "lock:monster_master:user:" + uid;
    }


    private String getUserHashDetailKey(String activityId, String dayStr) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return aId + ":monster_master:user:detail:" + dayStr;
    }

    private String getHashTotalKey(String activityId) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return aId + ":monster_master:total";
    }

    private String getZetTypeKey(String activityId) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return aId + ":monster_master";
    }


    private String getHashTestDayKey(String activityId) {
        return ACTIVITY_ID + ":test:uid:day";
    }


}
